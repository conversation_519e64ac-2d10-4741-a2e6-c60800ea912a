#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试指标30系列功能
"""

from save_info import SaveInfo
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_test_data():
    """创建测试数据来验证指标计算逻辑"""
    
    # 模拟股票数据
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    
    test_stocks = {
        # 股票1: 连续3天一字板
        "000001.SZ": {
            "preClose": [10.0] * 27 + [10.0, 11.0, 12.1],
            "open": [10.0] * 27 + [11.0, 12.1, 13.31],  # 最后3天一字涨停
            "high": [10.5] * 27 + [11.0, 12.1, 13.31],  # 最后3天一字板
            "low": [9.5] * 27 + [11.0, 12.1, 13.31],   # 最后3天一字板
            "close": [10.2] * 27 + [11.0, 12.1, 13.31], # 最后3天涨停
        },

        # 股票2: 昨日一字板，前日不是
        "000002.SZ": {
            "preClose": [20.0] * 29 + [20.0],
            "open": [20.0] * 29 + [22.0],     # 昨日一字涨停
            "high": [20.5] * 29 + [22.0],     # 昨日一字板
            "low": [19.5] * 29 + [22.0],      # 昨日一字板
            "close": [20.1] * 29 + [22.0],    # 昨日涨停
        },

        # 股票3: 一字起板（连续3天涨停，第一天是一字板）
        "000003.SZ": {
            "preClose": [15.0] * 27 + [15.0, 16.5, 18.15],
            "open": [15.0] * 27 + [16.5, 18.15, 19.97],   # 最后3天连续涨停
            "high": [15.5] * 27 + [16.5, 18.15, 19.97],   # 第一天一字板，后面正常涨停
            "low": [14.5] * 27 + [16.5, 18.0, 19.5],      # 第一天一字板，后面正常涨停
            "close": [15.1] * 27 + [16.5, 18.15, 19.97],  # 最后3天涨停
        },

        # 股票4: 非一字起板（连续涨停但第一天不是一字板）
        "000004.SZ": {
            "preClose": [25.0] * 27 + [25.0, 27.5, 30.25],
            "open": [25.0] * 27 + [26.0, 30.25, 33.28],   # 最后3天连续涨停
            "high": [25.5] * 27 + [27.5, 30.25, 33.28],   # 第一天不是一字板
            "low": [24.5] * 27 + [26.0, 30.25, 33.28],    # 第一天不是一字板
            "close": [25.1] * 27 + [27.5, 30.25, 33.28],  # 最后3天涨停
        }
    }
    
    return test_stocks, dates

def test_indicator_logic():
    """测试指标计算逻辑"""
    print("=== 测试指标30系列计算逻辑 ===\n")
    
    test_stocks, dates = create_test_data()
    
    # 创建SaveInfo实例
    save_info = SaveInfo()
    
    # 模拟数据处理
    results = {
        "指标30": {"个股": [], "最大天数": 0},
        "指标30A": {"个股": [], "数量": 0},
        "指标30B": {"个股": [], "数量": 0},
        "指标30C": {"个股": [], "数量": 0},
        "指标30D": {"个股": [], "数量": 0}
    }
    
    for stock_code, stock_data in test_stocks.items():
        # 创建DataFrame
        df = pd.DataFrame(stock_data, index=dates)
        
        # 添加计算字段
        from core.utility import get_stock_ratio
        limit_ratio = get_stock_ratio(stock_code)
        df["股票代码"] = stock_code
        df["最大涨幅"] = limit_ratio
        df["涨停价"] = (df["preClose"] * (1 + limit_ratio)).round(2)
        df["跌停价"] = (df["preClose"] * (1 - limit_ratio)).round(2)
        
        # 判断是否涨停和一字板
        df["是否涨停"] = df.apply(lambda x: round(x["close"], 2) == round(x["涨停价"], 2), axis=1)
        df["是否一字板"] = df["是否涨停"] & (df["open"] == df["high"]) & (df["high"] == df["low"]) & (df["low"] == df["close"])
        
        print(f"\n--- {stock_code} 数据分析 ---")
        print(f"涨停天数: {df['是否涨停'].sum()}")
        print(f"一字板天数: {df['是否一字板'].sum()}")
        print(f"最后5天一字板情况: {df['是否一字板'].tail().tolist()}")
        
        # 计算指标
        save_info._calculate_indicators(stock_code, df, results)
    
    print(f"\n=== 最终统计结果 ===")
    for key, value in results.items():
        print(f"{key}: {value}")
    
    return results

def test_real_data():
    """测试真实数据"""
    print("\n=== 测试真实数据 ===")
    
    try:
        save_info = SaveInfo()
        # 只测试少量股票以节省时间
        save_info.stock_list = save_info.stock_list[:10]  # 只测试前10只股票
        
        results = save_info.save_his_data()
        print("真实数据测试完成！")
        return results
        
    except Exception as e:
        print(f"真实数据测试失败: {e}")
        return None

if __name__ == "__main__":
    # 测试计算逻辑
    test_results = test_indicator_logic()
    
    # 测试真实数据（可选）
    print("\n" + "="*50)
    user_input = input("是否测试真实数据？(y/n): ")
    if user_input.lower() == 'y':
        real_results = test_real_data()
