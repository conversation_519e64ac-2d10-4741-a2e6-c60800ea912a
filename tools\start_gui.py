#!/usr/bin/env python3
"""
量化交易系统GUI启动脚本
检查依赖并启动GUI界面
"""

import sys
import os
import subprocess
import importlib

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'PyQt5',
        'pyzmq', 
        'msgpack',
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    return missing_packages

def install_dependencies():
    """安装缺失的依赖包"""
    print("\n正在安装GUI依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", 
            os.path.join(os.path.dirname(__file__), "gui_requirements.txt")
        ])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False

def start_gui():
    """启动GUI界面"""
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 导入并启动GUI
        from gui_main import QuantTradingMainWindow
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        app.setApplicationName("量化交易系统")
        app.setApplicationVersion("1.0.0")
        app.setStyle('Fusion')
        
        window = QuantTradingMainWindow()
        window.show()
        
        print("GUI界面启动成功!")
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"✗ 导入GUI模块失败: {e}")
        print("请检查依赖包是否正确安装")
        return False
    except Exception as e:
        print(f"✗ GUI启动失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("量化交易系统GUI启动器")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("✗ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查依赖
    print("\n检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n发现缺失的依赖包: {', '.join(missing_packages)}")
        
        # 询问是否自动安装
        try:
            response = input("\n是否自动安装缺失的依赖包? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                if not install_dependencies():
                    print("依赖包安装失败，请手动安装")
                    sys.exit(1)
            else:
                print("请手动安装依赖包:")
                print(f"pip install -r {os.path.join(os.path.dirname(__file__), 'gui_requirements.txt')}")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(1)
    
    # 启动GUI
    print("\n启动GUI界面...")
    start_gui()

if __name__ == "__main__":
    main()
