import sys
import platform
print(platform.architecture())
sys.path.append(rf"C:\Program Files\Tinysoft\AnalyseNG.NET")
import TSLPy310 as TSLPy3
# data = TSLPy3.RemoteExecute("return 1;", {})
# print(data)
import pandas as pd
import json
import time
from datetime import datetime, date, timedelta
import pandas as pd

class TslData:
    name = "TslData"
    def __init__(self) -> None:
        pass
    
    def encode_date(self, year: int, month: int, day: int):
        return TSLPy3.EncodeDate(year, month, day)
    
    def encode_time(self, hour: int, minute: int, second: int, millisecond: int=0):
        return TSLPy3.EncodeTime(hour, minute, second, millisecond)
    
    def decode_date(self, date: float):
        return TSLPy3.DecodeDate(date)
    
    def decode_time(self, time: float):
        return TSLPy3.DecodeTime(time)
    
    def decode_datetime(self, datetime: float):
        return TSLPy3.DecodeDateTime(datetime)
    

    def datetime_to_tiansoft_float(self, dt):
        """
        将 Python datetime 转为 天软浮点日期时间（如 42309.5）
        参数:
            dt: datetime 对象 或 pandas.Timestamp
        返回:
            float: 天软日期时间（从 1899-12-30 开始的天数）
        """
        if isinstance(dt, pd.Timestamp):
            dt = dt.to_pydatetime()
        # 天软日期系统：1899-12-30 是第 0 天
        epoch = datetime(1899, 12, 30)
        diff = dt - epoch
        
        # 总秒数 / 86400 得到天数（含小数）
        days = diff.days + (diff.seconds / 86400.0) + (diff.microseconds / 86400000000.0)
        return days

    def datetime_to_tiansoft_str(self, dt):
        """
        将 Python datetime 转为 天软 TSL 字符串格式：YYYYMMDD.HHMMT
        
        参数:
            dt: datetime 对象 或 pandas.Timestamp
        返回:
            str: 如 '20101231.0931T'
        """
        if isinstance(dt, pd.Timestamp):
            dt = dt.to_pydatetime()
        
        date_part = dt.strftime('%Y%m%d')      # 20101231
        time_part = dt.strftime('%H%M')        # 0931
        return f"{date_part}.{time_part}T"

    def remote_execute(self, code: str, params: dict, *args, **kwargs) -> dict:
        """执行代码
        Args:
            code: 代码
            params: 参数
        """
        data = TSLPy3.RemoteExecute(code, params, *args, **kwargs)
        if not data[0]:
            return data[1]
        else:
            raise Exception(data[2].decode("gbk"))
    
    def get_tick_data(self, stock_code: str, begt: str, endt: str, keys: list = None) -> pd.DataFrame:
        """获取tick数据"""
        if keys is None:
            keys = ["datetimetostr(['date']) as 'date'", "*"]
        keys = ",".join(keys)
        code = f"""
        SetSysParam(PN_Stock(),"{stock_code}");
        return Select {keys} from TradeTable DateKey
            {begt} to {endt}
            Of DefaultStockID() end;
        """
        data = self.remote_execute(code, {})
        df = pd.DataFrame(data)
        df.columns = [col.decode('utf-8') if isinstance(col, bytes) else col for col in df.columns]
        df['StockName'] = df['StockName'].str.decode('gbk', errors='ignore')
        return df
    
    def get_real_tick(self, stock_code: str) -> pd.DataFrame:
        """获取最新tick数据"""
        code = f"""
        SetSysParam(PN_Stock(),"{stock_code}");
        setsysparam(pn_date(),inttodate(20250829)-1);
        return TradeCount();
        """
        data = self.remote_execute(code, {})
        return data

    def get_market_data(self, stock_code: str, begt: str, endt: str) -> pd.DataFrame:
        """获取日线数据"""
        # setsysparam(pn_cycle(),cy_day());
        code = f"""
        SetSysParam(PN_Stock(),"{stock_code}");
        return Select *,datetimetostr(['date']) as 'date' from MarketTable DateKey
            {begt}T-1+18/24 to {endt}T+18/24
            Of DefaultStockID() end;
        """
        data = self.remote_execute(code, {})
        df = pd.DataFrame(data)
        df.columns = [col.decode('utf-8') if isinstance(col, bytes) else col for col in df.columns]
        df['StockName'] = df['StockName'].str.decode('gbk', errors='ignore')
        return df

    def get_bk(self, market: str="A股") -> list:
        """获取股票列表"""
        code = f"""
        return GetBK("{market}");
        """
        data = self.remote_execute(code, {})
        data = [x.decode('utf-8') for x in data]
        return data

    def n_day(self, stock_code: str, n: int,
                endt: str, time: str="16/24",
                field: str='"time",DateTimeToStr(sp_time()),"close",close(),"open",open(),"high",high(),"c-o",close()-open()',
                period: str="cy_day()") -> pd.DataFrame:
        """获取n日数据"""
        code = f"""
            SetSysParam(pn_stock(),"{stock_code}");
            SetSysParam(pn_Cycle(),{period});
            SetSysParam(pn_date(),inttodate({endt})+{time});
            N:={n}; //取100日
            Return Nday(N,{field});
        """
        data = self.remote_execute(code, {})
        df = pd.DataFrame(data)
        df.columns = [col.decode('utf-8') if isinstance(col, bytes) else col for col in df.columns]
        return df
    
    def n_day3(self, stock_code: str, n: int,
                endt: str=None,
                field: str='close()',
                period: str="cy_day()") -> pd.DataFrame:
        """获取n日数据"""
        if endt:
            cmd = f"SetSysParam(pn_date(),inttodate({endt}));"
        else:
            cmd = ""
        code = f"""
            SetSysParam(pn_stock(),"{stock_code}");
            SetSysParam(pn_Cycle(),{period});
            {cmd}
            N:={n}; //取100日
            Return Nday3(N,{field});
        """
        data = self.remote_execute(code, {})
        return data

    def trade_days(self, stock_code: str, begt: str, endt: str) -> int:
        # 获得两个时间间的交易日数（周期数）
        code = f"""
            begt:=IntToDate({begt});
            endt:=IntToDate({endt}) + 0.99;
            SetSysParam(pn_stock(),"{stock_code}");
            SetSysParam(pn_date(),EndT); 
            return TradeDays(begt,endt);
        """
        data = self.remote_execute(code, {})
        return data

if __name__ == "__main__":
    tsl = TslData()
    # df = tsl.n_day3("SZ300499", 100, field="cjbs", endt="20250829")
    df = tsl.get_real_tick("SZ300499")
    print(df)
    # df.to_csv("test2.csv", index=False)
    # print(df, len(df))
    # data = tsl.trade_days(begt="20250820", endt="20250827", stock_code="SZ300499")
    # print(data)
    # print(TSLPy3.DecodeDate(43567.0))