from core import context_manager
from core.utility import get_stock_ratio
import datetime
import numpy as np
import pandas as pd
from functools import lru_cache


class BaseStrategy:
    def __init__(self) -> None:
        self.qmt_data = context_manager.module_obj("qmt_data")
        self.qmt_trader = context_manager.module_obj("qmt_trader")
        self.tick_dct = {}
        self.cash_manager = context_manager.module_obj("cash_manager")
        # 时间序列管理器（DataFrame 模式）
        self.cash_ticks = self.cash_manager.cash_manger
        self.full_ticks = self.cash_manager.full_ticks
        self.base_df = self.cash_manager.base_df
        self.tsl_client = None
    
    # ==================== 辅助方法 ===================
    def today(self):
        return datetime.datetime.today().strftime("%Y%m%d")
    
    def gen_today_ts(self, hour: int, minute: int, second: int):
        dt = datetime.datetime.now().replace(hour=hour, minute=minute, second=second, microsecond=0)
        timestrap = dt.timestamp() * 1000
        return int(timestrap)
    
    def gen_today_tsl(self, hour: int, minute: int, second: int):
        dt = datetime.datetime.now().replace(hour=hour, minute=minute, second=second, microsecond=0)
        tsl = self.tsl_client.datetime_to_tiansoft_float(dt)
        return tsl

    def get_tsl_tick(self, stock_code: str, start_time: str, end_time: str):
        begt = self.time_str_to_timestamp(start_time, is_tsl=True)
        endt = self.time_str_to_timestamp(end_time, is_tsl=True)
        df = self.tsl_client.get_tick_data(stock_code, begt, endt)
        return df

    def time_str_to_timestamp(self, time_str: str, is_tsl: bool = False):
        if isinstance(time_str, int):
            return time_str
        if is_tsl:
            tsl_time = self.gen_today_tsl(hour=int(time_str[:2]), minute=int(time_str[2:4]), second=int(time_str[4:6]))
            return tsl_time
        timestrip = self.gen_today_ts(hour=int(time_str[:2]), minute=int(time_str[2:4]), second=int(time_str[4:6]))
        return timestrip
    
    def full_tick_df(self):
        df = pd.DataFrame.from_dict(self.full_ticks, orient='index')
        return df
    
    def get_col_name_index(self, col_name: str):
        return self.cash_ticks.col_pos.get(col_name)
    
    def get_ts_index(self, stock_code: str, timestamp: int):
        timestamp = self.time_str_to_timestamp(timestamp)
        return self.cash_ticks._find_timestamp_index(stock_code, timestamp)
    
    def last_index(self, stock_code: str):
            return self.cash_ticks.stock_row_counts.get(stock_code)
    
    def get_cell_value_by_index(self, stock_code: str, row_idx: int, column: str):
        return self.cash_ticks.get_cell_value_by_index(stock_code, row_idx, column)
    
    def get_cell_value_by_ts(self, stock_code: str, timestamp: int, column: str):
        index = self.get_ts_index(stock_code, timestamp)
        return self.cash_ticks.get_value_by_timestamp(stock_code, index, column)
    def last_row_df(self, stock_code: str):
        df =  self.valid_df(stock_code)
        row = df.iloc[-1]
        return row
    
    def latest_df(self, stock_code: str, count: int = 1):
        return self.cash_ticks.get_latest_data(stock_code, count)
    
    def valid_df(self, stock_code: str):
        return self.cash_ticks.get_valid_data(stock_code)
    
    def get_row_df_by_index(self, stock_code: str, start_idx: int, count: int = 1):
        return self.cash_ticks.get_rows_by_index(stock_code, start_idx, count)
    
    def get_row_df_by_price(self, stock_code: str, price: float, col_name="last_price"):
        df = self.cash_ticks.get_valid_data(stock_code)
        return df.loc[df[col_name] == price]

    def get_row_df_by_timestamp(self, stock_code: str, timestamp: int):
        return self.cash_ticks.get_data_by_timestamp(stock_code, timestamp)
    
    def get_row_by_ts_range(self, stock_code: str, start_ts: int, end_ts: int):
        return self.cash_ticks.get_data_by_timestamp_range(stock_code, start_ts, end_ts)


    def float_volume(self, stock_code: str) -> float:
        fv = self.full_ticks[stock_code]["float_volume"]
        return fv

    def limit_up_price(self, stock_code: str) -> float:
        try:
            row = self.base_df.loc[self.base_df["股票代码"] == stock_code]
            if not row.empty:
                return float(row.iloc[0]["涨停价"])
        except Exception:
            pass
        return 0.0
    
    def limit_ratio(self, stock_code: str) -> float:
        try:
            row = self.base_df.loc[self.base_df["股票代码"] == stock_code]
            if not row.empty:
                return float(row.iloc[0]["最大涨跌幅"])
        except Exception:
            pass
        return 0.0
    
    def pre_volume(self, stock_code: str) -> float:
        try:
            val = self.base_df.at[stock_code, "昨日成交量"]
            return val
        except Exception:
            return 0
        
    def buy(self, stock_code: str, volume: int, price: float, strategy_type: str):
        pass
    def sell(self, stock_code: str, volume: int, price: float, strategy_type: str):
        pass
    


    # ==================== 核心指标函数（DF模式）====================
    def f01(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        col_names = ["bid1_vol", "bid2_vol", "bid3_vol"]
        val = row_df[col_names].sum() * row_df["bid1_price"] / 100
        return round(val, 2)

    def f01b(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        numerator = row_df[["bid1_vol", "bid2_vol"]].sum()
        denominator = row_df[["bid1_vol", "bid2_vol"]].sum()
        if denominator == 0:
            return 0
        val = numerator / denominator
        return round(val, 2)

    def f02(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        numerator = row_df[["ask1_vol", "ask2_vol", "ask3_vol"]].sum()
        denominator = row_df[["bid1_vol", "bid2_vol", "bid3_vol"]].sum()
        if denominator == 0:
            return 0
        val = numerator / denominator
        return round(val, 2)

    def f03(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        numerator = row_df[["ask1_vol", "ask2_vol"]].sum()
        denominator = row_df[["bid1_vol", "bid2_vol"]].sum()
        if denominator == 0:
            return 0
        val = numerator / denominator
        return round(val, 2)

    def f04(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        numerator = row_df[["ask1_vol", "ask2_vol"]].sum()
        denominator = row_df[["bid1_vol", "bid2_vol"]].sum()
        if denominator == 0:
            return 0
        val = numerator / denominator
        return round(val, 2)

    def f05(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = row_df[["bid1_vol", "bid2_vol", "bid3_vol"]].sum() / fv * 100
        return round(val, 2)

    def f06(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        den = row_df["ask1_vol"]
        if den == 0:
            return 0
        val = row_df[["bid1_vol", "bid2_vol"]].sum() / den
        return round(val, 2)

    def f06b(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        den = row_df["bid1_vol"]
        if den == 0:
            return 0
        val = row_df[["bid1_vol", "bid2_vol"]].sum() / den
        return round(val, 2)

    def f07(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        bid_sum = row_df[["bid1_vol","bid2_vol","bid3_vol","bid4_vol","bid5_vol"]].sum()
        ask_sum = row_df[["ask1_vol","ask2_vol","ask3_vol","ask4_vol","ask5_vol"]].sum()
        if ask_sum == 0:
            return 0
        val = bid_sum / ask_sum
        return round(val, 2)

    def f08(self, stock_code: str):
        # 与 f07 相同（可能逻辑留作扩展），暂保持一致
        return self.f07(stock_code)

    def f09(self, stock_code: str):
        index = self.last_index(stock_code)
        net_volume = self.get_cell_value_by_index(stock_code, index, "net_volume")
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = net_volume / fv * 100
        return round(val, 2)
    
    def f10(self, stock_code: str):
        fv = self.float_volume(stock_code)
        volume = self.get_cell_value_by_index(stock_code, -1, "volume")
        if fv == 0 or volume is None:
            return 0
        val = volume / fv * 100
        return round(val, 2)

    def f11(self, stock_code: str):
        net_amount = self.get_cell_value_by_index(stock_code, -1, "net_amount")
        if not net_amount:
            return 0
        return round(net_amount, 2)

    def f12(self, stock_code: str):
        amount = self.get_cell_value_by_index(stock_code, -1, "amount")
        if not amount:
            return 0
        return round(amount, 2)

    def f13(self, stock_code: str):
        row_df = self.valid_df(stock_code)
        if row_df is None:
            return 0
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = row_df["bid1_vol"].max() / fv * 100
        return round(val, 2)

    def f13b(self, stock_code: str):
        df = self.valid_df(stock_code)
        if df is None or len(df) == 0:
            return 0
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = df["bid1_vol"].max() / fv * 100
        return round(val, 2)

    def f14(self, stock_code: str):
        ask1_vol = self.get_cell_value_by_index(stock_code, -1, "ask1_vol")
        if not ask1_vol:
            return 0
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = ask1_vol / fv * 100
        return round(val, 2)

    def f14b(self, stock_code: str):
        df = self.valid_df(stock_code)
        if df is None or len(df) == 0:
            return 0
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = df["ask1_vol"].max() / fv * 100
        return round(val, 2)

    def f15(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        val = row_df[["bid1_vol","bid2_vol","bid3_vol","bid4_vol","bid5_vol"]].sum()
        return round(val, 2)

    def f15b(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        fv = self.float_volume(stock_code)
        if fv == 0:
            return 0
        val = row_df[["bid1_vol","bid2_vol","bid3_vol","bid4_vol","bid5_vol"]].sum() / fv * 100
        return round(val, 2)

    def f16a(self, stock_code: str, start_time: str, end_time: str):
        return self.f16c(stock_code, start_time, end_time, 1)
    
    def f16c(self, stock_code: str, start_time: str, end_time: str, n: int=1):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] > df[f'ask{n}_price'].shift(1)
        total_volume = df.loc[condition, 'net_volume'].sum()
        return total_volume
    
    def f16b(self, stock_code: str, start_time: str, end_time: str):
        return self.f16d(stock_code, start_time, end_time, 1)
    
    def f16d(self, stock_code: str, start_time: str, end_time: str, n: int=1):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] > df[f'bid{n}_price'].shift(1)
        total_volume = df.loc[condition, 'net_volume'].sum()
        return total_volume
    
    def f17(self, stock_code: str, start_time: str, end_time: str):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] > df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_amount'].sum()
        return round(val/10000, 2)
    
    def f18(self, stock_code: str, start_time: str, end_time: str):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] < df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_amount'].sum()
        return round(val/10000, 2)
    def f19(self, stock_code: str, start_time: str, end_time: str):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] > df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_volume'].sum()
        return val
    def f19a(self, stock_code: str, start_time: str, end_time: str):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] == df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_volume'].sum()
        return val

    # 其他复杂函数暂时返回默认值，等DataFrame接口完善后再实现
    def f20(self, stock_code: str, start_time: str, end_time: str, n: float):
        vol = self.f19(stock_code, start_time, end_time)
        fv = self.float_volume(stock_code)
        if vol == 0 or fv == 0:
            return 0
        val = vol / fv * 100
        if val > n:
            return 1
        else:
            return 0
        
    def f20b(self, stock_code: str, start_time: str, end_time: str, n: float):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] < df[f'last_price'].shift(1)
        vol = df.loc[condition, 'net_volume'].sum()
        fv = self.float_volume(stock_code)
        if vol == 0 or fv == 0:
            return 0
        val = vol / fv * 100
        if val > n:
            return 1
        else:
            return 0

    def f21(self, stock_code: str, start_time: str, end_time: str, n: float):
        val  = self.f17(stock_code, start_time, end_time)
        if val > n:
            return 1
        else:
            return 0

    def f21b(self, stock_code: str, start_time: str, end_time: str, n: float):
        val  = self.f18(stock_code, start_time, end_time)
        if val > n:
            return 1
        else:
            return 0
    def f22(self, stock_code: str, start_time: str, end_time: str):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] < df[f'last_price'].shift(1)
        vol = df.loc[condition, 'net_volume'].max()
        fv = self.float_volume(stock_code)
        val = vol / fv * 100
        return round(val, 2)
    
    def f22b(self, stock_code: str, start_time: str, end_time: str):
        start_ts = self.time_str_to_timestamp(start_time)
        end_ts = self.time_str_to_timestamp(end_time)
        df = self.get_row_by_ts_range(stock_code, start_ts, end_ts)
        if df is None:
            return 0
        condition = df['last_price'] > df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_amount'].max()
        return round(val/10000, 2)
    
    # # 成交笔记获取不到 统计的是tick数量
    # def f23(self, stock_code: str, start_time: str, end_time: str, n: int):
    #     df = self.get_tsl_tick(stock_code, start_time, end_time)
    #     if df is None:
    #         return 0
    #     count = (df['vol'] < n).sum()
    #     return count
    
    # # 成交笔记获取不到 统计的是tick数量
    # def f24(self, stock_code: str, start_time: str, end_time: str):
    #     df = self.get_tsl_tick(stock_code, start_time, end_time)
    #     if df is None:
    #         return 0
    #     condition = df['price'] > df[f'price'].shift(1)
    #     count = condition.sum()
    #     return count
    # # 成交笔记获取不到 统计的是tick数量
    # def f25(self, stock_code: str, start_time: str, end_time: str):
    #     df = self.get_tsl_tick(stock_code, start_time, end_time)
    #     if df is None:
    #         return 0
    #     condition = df['price'] < df[f'price'].shift(1)
    #     count = condition.sum()
    #     return count
    
    # # 指标26:tick对应的成交笔数  成交笔记获取不到 
    # def f26(self, stock_code: str, time: str):
    #     today = self.today()
    #     code = f"""
    #     SetSysParam(PN_Stock(),"{stock_code}");
    #     setsysparam(pn_date(),inttodate({today})+0.99);
    #     setsysparam(pn_cycle(),cy_3s());
    #     return TradeCount();
    #     """
    #     data = self.remote_execute(code, {})
    #     return data

    # 指标27:昨日总成交笔数
    def f27(self, stock_code: str):
        today = self.today()
        code = f"""
        SetSysParam(PN_Stock(),"{stock_code}");
        setsysparam(pn_date(),inttodate({today})-1);
        return TradeCount();
        """
        data = self.remote_execute(code, {})
        return data

    def f28(self, stock_code: str):
        return self.pre_volume(stock_code)

    def f29(self, stock_codes: list, time: str):
        timestamp = self.time_str_to_timestamp(time)
        code_lst = []
        for stock_code in stock_codes:
            index = self.get_ts_index(stock_code, timestamp)
            val = self.get_cell_value_by_index(stock_code, index, "last_price")
            limit_up = self.limit_up_price(stock_code)
            if round(val, 2) == round(limit_up, 2):
                code_lst.append(stock_code)
        return code_lst
    # 指标30:昨日最大一字连板天数个股  开盘及涨停 无炸板(第一天也是) 
    def f30(self):
        results, _ = self.get_his_data()
        return results["指标30"]
    # 指标30A，昨日一字板   个股代码和数量
    def f30a(self):
        results, _ = self.get_his_data()
        return results["指标30A"]
    # 指标30B，前一日一字板 个股代码和数量
    def f30b(self):
        results, _ = self.get_his_data()
        return results["指标30B"]
    # 指标30C，昨日一字板前日最高价小于前日涨停价  个股代码和数量
    def f30c(self):
        results, _ = self.get_his_data()
        return results["指标30C"]
    # 指标30D，一字起板：第一个涨停板为一字板的个股。 个股代码和数量
    def f30d(self):
        results, _ = self.get_his_data()
        return results["指标30D"]
    def f30d(self, stock_code: str):
        df = pd.DataFrame.from_dict(self.full_ticks, orient='index')
        # 一字涨停
        condition = (df['high'] == df['limit_up_price']== df['low'])
        if condition.empty:
            return 0
        return df[condition]
    
    def f31(self, stock_code: str):
        index = self.last_index(stock_code)
        bid1_vol = self.get_cell_value_by_index(stock_code, index, "bid1_vol")
        fv = self.float_volume(stock_code)
        val = bid1_vol / fv * 100
        return round(val, 2)
    
    def f32(self, stock_code: str, n: int):
        df = self.latest_df(stock_code,count=n)
        bid1_vol = df["bid1_vol"].min()
        fv = self.float_volume(stock_code)
        val = bid1_vol / fv * 100
        return round(val, 2)
    
    def f33(self, stock_code: str, n: int):
        df = self.latest_df(stock_code,count=n)
        bid1_vol = df["bid1_vol"].max()
        fv = self.float_volume(stock_code)
        val = bid1_vol / fv * 100
        return round(val, 2)
    
    def f34(self, stock_code: str, n: int):
        df = self.latest_df(stock_code,count=n)
        bid1_price = df["bid1_price"].min()
        open = df.at[df.index[0], "open"]
        val = (bid1_price / open -1 )* 100
        return round(val, 2)
    
    def f35(self, stock_code: str, n: int):
        df = self.latest_df(stock_code,count=n)
        bid1_price = df["bid1_price"].max()
        open = df.at[df.index[0], "open"]
        val = (bid1_price / open -1 )* 100
        return round(val, 2)
    
    def f36(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        val = (row_df["bid1_price"] / row_df["low"] - 1) * 100
        return round(val, 2)


    
    def f37(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        if row_df is None:
            return 0
        val = (row_df["bid1_price"] / row_df["high"] - 1) * 100
        return round(val, 2)
    
    def f37b(self, stock_code: str, time: str):
        timestamp = self.time_str_to_timestamp(time)
        row_df = self.get_row_df_by_timestamp(stock_code, timestamp)
        if row_df is None:
            return 0
        val = (row_df["bid1_price"] / row_df["high"] - 1) * 100
        return round(val, 2)

    def f37c(self, stock_code: str, time: str):
        timestamp = self.time_str_to_timestamp(time)
        index = self.get_ts_index(stock_code, timestamp)
        df = self.get_row_df_by_index(stock_code, 0, index)
        if df.empty:
            return 0
        open = df.at[df.index[0], "open"]
        val = (df["bid1_price"].max() / open - 1) * 100
        return round(val, 2)
    
    def f38(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        val = (row_df["open"] / row_df["low"] - 1) * 100
        return round(val, 2)
    
    def f39(self, stock_code: str):
        row_df = self.last_row_df(stock_code)
        val = row_df["amount"] / row_df["volume"] *100
        return round(val, 2)

    def f40(self, stock_code: str):
        df = self.valid_df(stock_code)
        condition = df['last_price'] > df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_amount'].sum() / df.loc[condition, 'net_volume'].sum() * 100
        return round(val, 2) 

    def f41(self, stock_code: str):
        df = self.valid_df(stock_code)
        condition = df['last_price'] < df[f'last_price'].shift(1)
        val = df.loc[condition, 'net_amount'].sum() / df.loc[condition, 'net_volume'].sum() * 100
        return round(val, 2) 

    def f42(self, stock_code: str):
        df = self.valid_df(stock_code)
        condition = df['bid1_price'] == self.limit_up_price(stock_code)
        if condition.any():
            first_index = condition.idxmax()  # 返回第一个 True 的索引
            ts_value = df.loc[first_index, 'timestamp']
        else:
            ts_value = None
        return ts_value

    def f43(self, stock_code: str):
        df = self.valid_df(stock_code)
        condition = df['ask1_price'] == self.limit_up_price(stock_code)
        if condition.any():
            first_index = condition.idxmax()  # 返回第一个 True 的索引
            ts_value = df.loc[first_index, 'timestamp']
        else:
            ts_value = None
        return ts_value

    def f44(self, stock_code: str):
        df = self.valid_df(stock_code)
        if df.empty:
            return None
        limit_up_price = self.limit_up_price(stock_code)
        is_limit = (df['last_price'] == limit_up_price)
        if not is_limit.any():
            return None  # 从未涨停
        first_limit_idx = is_limit[is_limit].index[0]  # 第一个涨停的 index
        # 2. 找“炸板”：从第一个涨停之后，第一个 price < high_limit
        after_limit = df.loc[first_limit_idx:]  # 视图，不复制
        is_broken = (after_limit['last_price'] < limit_up_price)
        if not is_broken.any():
                return None  # 涨停后未跌破 → 未炸板
        first_broken_idx = is_broken.idxmax()  # 第一个炸板时刻的 index
        # 3. 找炸板后，第一次 bid1 == high_limit
        after_broken = df.loc[first_broken_idx:]  # 视图
        is_bid_on_limit = (after_broken['bid1_price'] == limit_up_price)
        if not is_bid_on_limit.any():
            return None  # 炸板后未重新封板
        re_limit_idx = is_bid_on_limit.idxmax()  # 第一次买一封板
        return df.loc[re_limit_idx, 'timestamp']  # 返回 ts 值

    
    def f45(self, stock_code: str):
        df = self.valid_df(stock_code)
        if df.empty:
            return None
        limit_up_price = self.limit_up_price(stock_code)
        is_limit = (df['last_price'] == limit_up_price)
        if not is_limit.any():
            return None  # 从未涨停
        first_limit_idx = is_limit[is_limit].index[0]  # 第一个涨停的 index
        # 2. 找“炸板”：从第一个涨停之后，第一个 price < high_limit
        after_limit = df.loc[first_limit_idx:]  # 视图，不复制
        is_broken = (after_limit['last_price'] < limit_up_price)
        if not is_broken.any():
                return None  # 涨停后未跌破 → 未炸板
        first_broken_idx = is_broken.idxmax()  # 第一个炸板时刻的 index
        # 3. 找炸板后，第一次 bid1 == high_limit
        after_broken = df.loc[first_broken_idx:]  # 视图
        is_bid_on_limit = (after_broken['ask1_price'] == limit_up_price)
        if not is_bid_on_limit.any():
            return None  # 炸板后未重新封板
        re_limit_idx = is_bid_on_limit.idxmax()  # 第一次买一封板
        return df.loc[re_limit_idx, 'timestamp']  # 返回 ts 值

    def f45b(self, stock_code: str, n: int = 1):
        ts = self.f45(stock_code)
        if ts:
            index = self.get_ts_index(stock_code, ts)
            df = self.get_row_df_by_index(stock_code, index, n)
            if not df.empty:
                return df["bid1_vol"].sum()
        return 0

    def f45c(self, stock_code: str, n: int = 1):
        ts = self.f45(stock_code)
        if ts:
            index = self.get_ts_index(stock_code, ts)
            df = self.get_row_df_by_index(stock_code, index, n)
            if not df.empty:
                return df["net_volume"].sum()
        return 0

    def f46(self, stock_code: str):
        ts = self.f42(stock_code)
        if ts:
            index = self.get_ts_index(stock_code, ts)
            low = self.get_cell_value_by_index(stock_code, index, "low")
            return low
        return 0

    def f47(self, stock_code: str):
        ts = self.f43(stock_code)
        if ts:
            index = self.get_ts_index(stock_code, ts)
            df = self.get_row_df_by_index(stock_code, index, -1)
            low = df["last_price"].min()
            return low
        return 0

    def f47b(self, stock_code: str):
        low_1 = self.f47(stock_code)
        low_0 = self.f46(stock_code)
        if low_1 and low_0:
            return round(low_1 / low_0, 2)
        return 0

    def f48a(self, stock_code: str):
        df = self.valid_df(stock_code)
        limit_up_price = self.limit_up_price(stock_code)
        is_limit = (df['last_price'] == limit_up_price)
        val = df[is_limit]["net_volume"].sum()
        return val

    def f48b(self, stock_code: str):
        fv = self.float_volume(stock_code)
        val = self.f48a(stock_code) / fv * 100
        return round(val, 2)

    def f48e(self, stock_code: str):
        ts = self.f42(stock_code)
        if ts:
            index = self.get_ts_index(stock_code, ts)
            bid1_vol = self.get_cell_value_by_index(stock_code, index, "bid1_vol")
            fv = self.float_volume(stock_code)
            val = bid1_vol / fv * 100
            return round(val, 2)
        return 0

    def f48b(self, stock_code: str):
        df = self.valid_df(stock_code)
        condition = df['bid1_price'] == self.limit_up_price(stock_code)
        val = df[condition]['bid1_vol'].sum()
        return val

    def f48c(self, stock_code: str):
        fv = self.float_volume(stock_code)
        df = self.valid_df(stock_code)
        last_row = df.iloc[-1]      # 当前行
        prev_row = df.iloc[-2]      # 上一笔
        val = (last_row['bid1_vol'] -last_row['net_volume']- prev_row['bid1_vol']) / fv * 100
        return round(val, 2)

    def f49(self, stock_code: str):
        df = self.valid_df(stock_code)
        ts = self.f42(stock_code)
        start_idx = self.get_ts_index(stock_code, ts)
        new_df = df.iloc[start_idx:]
        are_equal = (new_df['last_price'].mim() == new_df['last_price'].max())
        if are_equal:
            val = new_df["bid1_vol"].sum() / new_df["net_volume"].sum() *100
            return round(val, 2)
        return 0

    def f50(self, stock_code: str, n: int):
        df = self.valid_df(stock_code)
        new_df = df.iloc[-n:]
        val = new_df["net_volume"].sum() / new_df.iloc[-1]["bid1_vol"] *100
        return round(val, 2)

    def f51(self, stock_code: str, n: int):
        df = self.valid_df(stock_code)
        new_df = df.iloc[-n:]
        fv = self.float_volume(stock_code)
        val = new_df["net_volume"].sum() / fv * 100
        return round(val, 2)

    def f52(self, stock_code: str):
        df = self.valid_df(stock_code)
        limit_up_price = self.limit_up_price(stock_code)
    # 1. 提前获取 numpy 数组（只读，不修改 df）
        try:
            prices = df['last_price'].values      # shape: (N,)
            volumes = df['net_volume'].values     # shape: (N,)
        except KeyError:
            return 0.0
        n = len(prices)
        limit_mask = (prices == limit_up_price)
        if not limit_mask.any() or n < 2:
            return 0.0
        first_limit_pos = np.argmax(limit_mask)  # 第一个 True 的位置（比 .index[0] 快）
        # 3. 从 first_limit_pos 开始，找第一个 price < limit_up_price
        broken_pos = -1
        for i in range(first_limit_pos, n):
            if prices[i] < limit_up_price:
                broken_pos = i
                break
        if broken_pos == -1:
            return 0.0  # 未炸板
        # 4. 计算 [first_limit_pos, broken_pos) 区间内的 yang - yin
        # 即：从第一个涨停开始，到炸板前一笔
        start = first_limit_pos
        end = broken_pos  # 不包含炸板时刻
        # 向量化计算 diff 和 sign
        price_diff = prices[start+1:end] - prices[start:end-1]  # diff = p[i] - p[i-1]
        vol_segment = volumes[start+1:end]  # net_volume 对应上涨/下跌段
        # 符号：+1 涨，-1 跌，0 平
        signs = np.sign(price_diff)
        # 加权求和
        result = np.sum(vol_segment * signs)
        return float(result)

    def f53(self, stock_code: str, time: str):
        ts = self.time_str_to_timestamp(time)
        index = self.get_ts_index(stock_code, ts)
        if not index:
            return 0
        df = self.valid_df(stock_code)
        val = df.iloc[index]["net_volume"] / df.iloc[index - 1]["bid1_vol"]
        return round(val, 2)

    def f54(self, stock_code: str, time: str, n: int):
        ts = self.time_str_to_timestamp(time)
        index = self.get_ts_index(stock_code, ts)
        if not index:
            return 0
        df = self.valid_df(stock_code)
        val = df.iloc[index]["net_volume"] / df.iloc[index+1:index+n]["net_volume"].max()
        return round(val, 2)

    def f55(self, stock_code: str, time: str, n: int):
        ts = self.time_str_to_timestamp(time)
        index = self.get_ts_index(stock_code, ts)
        if not index:
            return 0
        df = self.valid_df(stock_code)
        val = df.iloc[index]["last_price"] / df.iloc[index+1: index+n]["last_price"].max()
        return round(val, 2)
    # 指标56，指定时刻的成交笔数/该tick后的N笔的最大成交笔数  成交笔试暂时获取不到
    # def f56(self, stock_code: str):
    #     ts = self.time_str_to_timestamp(time)
    #     index = self.get_ts_index(stock_code, ts)
    #     if not index:
    #         return 0
    #     df = self.valid_df(stock_code)
    #     val = df.iloc[index]["num_transaction"] / df.iloc[index+1: index+n]["last_price"].max()
    #     return round(val, 2)
    def f57(self, stock_code: str):
        _, data = self.get_his_data()
        row = data[stock_code].iloc[-1]
        info = {
            "涨停价": row["涨停价"],
            "跌停价": row["跌停价"],
            # "流通股本": row["流通股本"],
            # "总股本": row["总股本"],
            "最大涨跌幅": round(row["high"] / row["preClose"] - 1, 2),
            "昨日收盘价": row["close"],
            "昨日最高价": row["high"],
            "昨日最低价": row["low"],
            "昨日开盘价": row["open"],
            "昨日成交量": row["volume"],
            "昨日成交额": row["amount"],
        }
        return info

    def f57a(self, stock_code: str, time: str):
        ts = self.time_str_to_timestamp(time)
        index = self.get_ts_index(stock_code, ts)
        df = self.valid_df(stock_code)
        row = df.iloc[index]
        info = {
            "last_price": row["last_price"],
            "bid1_price": row["bid1_price"],
        }
        return info

    def f57b(self, stock_code: str, n: int=0):
        df = self.valid_df(stock_code)
        max_index = self.last_index(stock_code)
        if n > max_index:
            return 0
        last_price = df.iloc[n, self.get_col_name_index("last_price")]
        return last_price

    def f58(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        val = df.loc[df["last_price"] == price, "net_volume"].max()
        if pd.isna(val):
            return 0
        return val
    # 指标58B，计算成交的最大量对应的指定价和时分秒  
    def f58b(self, stock_code: str):
        df = self.valid_df(stock_code)
        max_vol = df["net_volume"].max()
        row = df.loc[df["net_volume"] == max_vol].iloc[0]
        info = {
            "last_price": row["last_price"],
            "timestamp": row["timestamp"],
        }
        return info
    
    def f59(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        val = df.loc[df["last_price"] == price, "bid1_vol"].max()
        if pd.isna(val):
            return 0
        return val

    def f60(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        val = df.loc[df["last_price"] == price, "ask1_vol"].max()
        if pd.isna(val):
            return 0
        return val

    def f61(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        rows = df.loc[df["last_price"] == price]
        if rows.empty:
            return {}
        info = {
            "bid2_vol": rows["bid2_vol"].max(),
            "bid3_vol": rows["bid3_vol"].max(),
            "bid4_vol": rows["bid4_vol"].max(),
            "bid5_vol": rows["bid5_vol"].max(),
        }
        return info
    
    # 全市场
    def f63a(self, n: float, x: int):
        df = self.full_tick_df()
        new_df = df[(df["last_price"] == df["high"] & (df["change"] > n))]
        df_sorted = new_df.sort_values(by='change', ascending=False)
        return df_sorted.iloc[:x]

    def f63b(self, n: float, x: int):
        df = self.full_tick_df()
        new_df = df[(df["last_price"] == df["high"] & (df["change"] > (df["limit_ratio"]* 100 -n)))]
        df_sorted = new_df.sort_values(by='change', ascending=False)
        return df_sorted.iloc[:x]
    # 昨日信息统计
    # def f64(self):

    def f66(self, stock_code: str):
        tick_dct = self.full_ticks.get(stock_code, None)
        # 1. 提取固定涨停价
        limit_up = tick_dct.get("limit_up_price", 0)
        # 没有涨停过不计算
        if not limit_up or tick_dct["high"] != limit_up:
            return 0
        df = self.valid_df(stock_code)
        # 2. 判断炸板
        is_boom = (df["last_price"].shift(1) == limit_up) & (df["last_price"] != limit_up)
        # 3. 统计炸板次数
        boom_count = is_boom.sum()
        # # 4. (可选) 获取炸板时刻数据
        # boom_df = df[is_boom].copy()
        return boom_count

    # 指定时刻的涨跌幅
    def f67(self, stock_code: str, time: str):
        change = self.get_cell_value_by_ts(stock_code, time, "change")
        return change
    
    # 最后一次炸板时间 未涨停返回0 涨停未占比返回1 涨停后存在炸板返回ts
    def f69(self, stock_code: str):
        tick_dct = self.full_ticks.get(stock_code, None)
        limit_up = tick_dct.get("limit_up_price", 0)
        if not limit_up or tick_dct["high"] != limit_up:
            return 0
        elif tick_dct["last_price"] == limit_up:
            return 1
        df = self.valid_df(stock_code)
        is_boom = (df["last_price"].shift(1) == limit_up) & (df["last_price"] != limit_up)
        pos = np.where(is_boom)[0]
        last_boom_idx = pos[-1]
        ts_idx = self.get_col_name_index("timestamp")
        timestamp = df.iloc[last_boom_idx, ts_idx]
        return timestamp

    def f70(self, stock_code: str, start_time: str, end_time: str):
        fv = self.full_ticks.get(stock_code, {}).get("float_volume", 0)
        if not fv:
            return 0
        st_idx = self.get_ts_index(stock_code, start_time)
        ed_idx = self.get_ts_index(stock_code, end_time)
        df = self.valid_df(stock_code).iloc[st_idx:ed_idx]
        is_boom = df["last_price"] >= df["last_price"].shift(1)
        val = df[is_boom]["net_volume"].sum() / fv * 100
        return val


    def f71(self, stock_code: str, start_time: str, end_time: str, n: int):
        st_idx = self.get_ts_index(stock_code, start_time)
        ed_idx = self.get_ts_index(stock_code, end_time)
        df = self.valid_df(stock_code).iloc[st_idx:ed_idx]
        is_boom = df["last_price"] >= df["last_price"].shift(1)
        val = df[is_boom]["net_amount"].sum() / 10000
        return round(val, 2)


    def f72(self, stock_code: str, start_time: str, end_time: str, n: int):
        st_idx = self.get_ts_index(stock_code, start_time)
        ed_idx = self.get_ts_index(stock_code, end_time)
        df = self.valid_df(stock_code).iloc[st_idx:ed_idx]
        is_boom = df["last_price"] < df["last_price"].shift(1)
        val = df[is_boom]["net_amount"].sum() / 10000
        return round(val, 2)

    # z这不是竞价的时候吗？ 092500
    def f73(self, stock_code: str, n: int):
        # 暂时返回0，需要实现全量数据遍历功能
        return 0

    # 这个计算可能会很慢
    def f74(self, stock_codes: list[str], func, time, *args, **kwargs):
        lst = []
        for stock_code in stock_codes:
            val = func(stock_code, *args, time=time, **kwargs)
            if val:
                lst.append({stock_code: val})
        # 从大到小排序
        # 取第一个val
        orted_data = sorted(lst, key=lambda d: next(iter(d.values())), reverse=True)
        # 取最大val
        # sorted_data = sorted(lst, key=lambda d: max(d.values()), reverse=True)
        return orted_data

    def f75(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        is_boom = df["bid1_price"] == price
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            open = df.iloc[idx, self.get_col_name_index("open")]
            low = df.iloc[idx, self.get_col_name_index("low")]
            val = round((open / low -1) * 100, 2)
            return val
        return 0

    def f76(self, stock_code: str, price: float, n: int, x: int):
        df = self.valid_df(stock_code)
        is_boom = df["bid1_price"] == price
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            bid1_vol_n = df.iloc[idx + n, self.get_col_name_index("bid1_vol")]
            bid1_vol_x = df.iloc[idx + x, self.get_col_name_index("bid1_vol")]
            val = round((bid1_vol_n / bid1_vol_x), 2)
            return val
        return 0
    
    def f77(self, stock_code: str, price: float, n: int, x: int):
        df = self.valid_df(stock_code)
        is_boom = df["bid1_price"] == price
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            vol = df.iloc[idx + 1, idx+n+1]["net_volume"].sum()
            bid1_vol_x = df.iloc[idx + x, self.get_col_name_index("bid1_vol")]
            val = round((vol / bid1_vol_x), 2)
            return val
        return 0

    # 指标78，昨日涨停并且昨日收盘价大于开盘价  昨日数据先不处理

    def f79(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        is_boom = df["bid1_price"] == price
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            row_df = df.iloc[idx]
            bid1_vol = row_df[self.get_col_name_index("bid1_vol")]
            bid1_amount = row_df[self.get_col_name_index("bid1_amount")]
            val = round((bid1_vol * bid1_amount / 100), 2)
            return val
        return 0


    def f80(self, stock_code: str, price: float):
        df = self.valid_df(stock_code)
        is_boom = df["bid1_price"] == price
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            fv = self.float_volume(stock_code)
            bid1_vol = df.iloc[idx, self.get_col_name_index("bid1_vol")]
            val = round((bid1_vol / fv * 100), 2)
            return val
        return 0
    
    def f81(self, stock_code: str, time: str):
        idx = self.get_ts_index(stock_code, time)
        df = self.valid_df(stock_code)
        row_df = df.iloc[idx]
        last_price = row_df[self.get_col_name_index("last_price")]
        low = row_df[self.get_col_name_index("low")]
        val = round(((last_price - low) / low ) * 100, 2)
        return val
      
    def f82(self, stock_code: str):
        df = self.valid_df(stock_code)
        bid_cols = ['bid1_vol', 'bid2_vol', 'bid3_vol', 'bid4_vol', 'bid5_vol']
        ask_cols = ['ask1_vol', 'ask2_vol', 'ask3_vol', 'ask4_vol', 'ask5_vol']
        total_bid = df[bid_cols].sum(axis=1)  # 每行求和
        total_ask = df[ask_cols].sum(axis=1)
        val = (total_bid - total_ask) / total_ask
        return round(val, 2)

    def f83(self, stock_code: str):
        df = self.valid_df(stock_code)
        volume = self.full_ticks[stock_code]["volume"]
        ask_cols = ['ask1_vol', 'ask2_vol', 'ask3_vol', 'ask4_vol', 'ask5_vol']
        total_ask = df[ask_cols].sum(axis=1)
        val = (volume - total_ask) / total_ask
        return round(val, 2)
    # 指标84，昨日成交量最大的单笔tick对应的成交价
    # 指标85，卖一价=跌停价的卖一量  第一次
    def f85(self, stock_code: str):
        limit_down = self.full_ticks[stock_code]["limit_down_price"]
        df = self.valid_df(stock_code)
        is_boom = df["ask1_price"] == limit_down
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            val = df.iloc[idx, self.get_col_name_index("ask1_vol")]
            return val
        return 0
    
    def f85a(self, stock_code: str):
        fv = self.float_volume(stock_code)
        val = self.f85(stock_code) / fv * 100
        return round(val, 2)
    # 指标85B，卖一价=跌停价的单笔成交量 第一次
    def f85b(self, stock_code: str):
        limit_down = self.full_ticks[stock_code]["limit_down_price"]
        df = self.valid_df(stock_code)
        is_boom = df["ask1_price"] == limit_down
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            val = df.iloc[idx, self.get_col_name_index("net_volume")]
            return val
        return 0
    
    # 指标85C，卖一价=跌停价的连续N笔成交量  85..都是第一次吗？这个是 第一次跌停后N笔吗
    def f85c(self, stock_code: str, n: int):
        limit_down = self.full_ticks[stock_code]["limit_down_price"]
        df = self.valid_df(stock_code)
        is_boom = df["ask1_price"] == limit_down
        pos = np.where(is_boom)[0]
        if len(pos) > 0:
            idx = pos[0]
            val = df.iloc[idx + 1, idx + n +1]["net_volume"].sum()
            return val
        return 0

    def f85d(self, stock_code: str, n: int):
        df = self.valid_df(stock_code)
        limit_down = self.full_ticks[stock_code]["limit_down_price"]
        val = df[df["ask1_price"] == limit_down]["ask1_vol"].max()
        return val

    def f85f(self, stock_code: str):
        df = self.valid_df(stock_code)
        limit_down = self.full_ticks[stock_code]["limit_down_price"]
        val = df[df["ask1_price"] == limit_down]["net_volume"].sum()
        return val

    def f85g(self, stock_code: str):
        df = self.valid_df(stock_code)
        limit_down = self.full_ticks[stock_code]["limit_down_price"]
        val = df[df["ask1_price"] == limit_down]["bid1_vol"].sum()
        return val

    def f86(self):
        stock_code = "000001.SH"
        d = self.qmt_data.get_full_tick([stock_code])
        return d[stock_code]["lastPrice"]

    def f90(self, stock_code: str):
        # 获取第一条记录（开盘数据）
        change = self.get_cell_value_by_index(stock_code, 0, "change")
        if change:
            return change
        return 0

    def f91(self, stock_code: str):
        tick_dct = self.full_ticks.get(stock_code, {})
        limit_ratio = tick_dct.get("limit_ratio", 0)
        if not limit_ratio:
            return 0
        val = tick_dct["change"] / limit_ratio
        return round(val, 2)

    @lru_cache(maxsize=3)
    def get_his_data(self):
        # 获取更多天数的历史数据以便计算连板天数
        data = self.qmt_data.get_market_data_ex(
            stock_list=self.stock_list,
            period="1d",
            count=30  # 获取30天数据用于计算连板天数
        )

        # 存储各个指标的结果
        results = {
            "指标30": {"个股": [], "最大天数": 0},
            "指标30A": {"个股": [], "数量": 0},
            "指标30B": {"个股": [], "数量": 0},
            "指标30C": {"个股": [], "数量": 0},
            "指标30D": {"个股": [], "数量": 0}
        }
    
        for stock_code, df in data.items():
            if df.empty or len(df) < 3:
                continue
            # 添加必要的计算字段
            limit_ratio = get_stock_ratio(stock_code)
            df["股票代码"] = stock_code
            df["最大涨幅"] = limit_ratio
            df["涨停价"] = (df["preClose"] * (1 + limit_ratio)).round(2)
            df["跌停价"] = (df["preClose"] * (1 - limit_ratio)).round(2)
            # 判断是否涨停
            df["是否涨停"] = df.apply(lambda x: round(x["close"], 2) == round(x["涨停价"], 2), axis=1)
            # 判断是否一字板（开盘价=最高价=最低价=收盘价，且涨停）
            df["是否一字板"] = df["是否涨停"] & (df["open"] == df["high"]) & (df["high"] == df["low"]) & (df["low"] == df["close"])

            # 按时间排序（最新的在最后）
            df = df.sort_index()

            # 计算各个指标
            self._calculate_indicators(stock_code, df, results)

        # 保存结果
        return results, data

    def _calculate_indicators(self, stock_code, df, results):
        """计算单个股票的各项指标"""
        if len(df) < 3:
            return

        # 获取最近几天的数据
        latest_day = df.iloc[-1]  # 昨日数据
        prev_day = df.iloc[-2] if len(df) >= 2 else None  # 前一日数据

        # 指标30A: 昨日一字板
        if latest_day["是否一字板"]:
            results["指标30A"]["个股"].append(stock_code)
            results["指标30A"]["数量"] += 1

        # 指标30B: 前一日一字板
        if prev_day is not None and prev_day["是否一字板"]:
            results["指标30B"]["个股"].append(stock_code)
            results["指标30B"]["数量"] += 1

        # 指标30C: 昨日一字板且前日最高价小于前日涨停价
        if (latest_day["是否一字板"] and prev_day is not None and
            round(prev_day["high"], 2) < round(prev_day["涨停价"], 2)):
            results["指标30C"]["个股"].append(stock_code)
            results["指标30C"]["数量"] += 1

        # 指标30D: 一字起板（从昨日开始连续涨停序列的第一个涨停为一字板）
        first_consecutive_limit_up_is_yizi = self._check_first_consecutive_limit_up_is_yizi(df)
        if first_consecutive_limit_up_is_yizi:
            results["指标30D"]["个股"].append(stock_code)
            results["指标30D"]["数量"] += 1

        # 指标30: 计算最大一字连板天数
        max_consecutive_days = self._calculate_max_consecutive_yizi_days(df)
        if max_consecutive_days > results["指标30"]["最大天数"]:
            results["指标30"]["最大天数"] = max_consecutive_days
            results["指标30"]["个股"] = [stock_code]
        elif max_consecutive_days == results["指标30"]["最大天数"] and max_consecutive_days > 0:
            results["指标30"]["个股"].append(stock_code)

    def _check_first_consecutive_limit_up_is_yizi(self, df):
        """检查从昨日开始连续涨停序列的第一个涨停是否为一字板"""
        if df.empty:
            return False
        # 从最新日期（昨日）开始往前找连续涨停序列
        limit_up_flags = df["是否涨停"].values
        yizi_flags = df["是否一字板"].values

        # 如果昨日不是涨停，直接返回False
        if not limit_up_flags[-1]:
            return False

        # 从最新日期往前找连续涨停的起始位置
        consecutive_start_idx = len(limit_up_flags) - 1
        for i in range(len(limit_up_flags) - 1, -1, -1):
            if limit_up_flags[i]:
                consecutive_start_idx = i
            else:
                break

        # 检查连续涨停序列的第一个涨停是否为一字板
        return yizi_flags[consecutive_start_idx]

    def _calculate_max_consecutive_yizi_days(self, df):
        """计算最大连续一字板天数"""
        if df.empty:
            return 0

        # 获取一字板标记
        yizi_flags = df["是否一字板"].values

        max_consecutive = 0
        current_consecutive = 0

        # 从最新日期往前计算连续天数
        for i in range(len(yizi_flags) - 1, -1, -1):
            if yizi_flags[i]:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

   