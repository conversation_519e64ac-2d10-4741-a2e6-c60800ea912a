"""
量化交易系统GUI主界面
使用PyQt5开发的专业级量化交易管理工具
"""

import sys
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import traceback

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QTabWidget, QLabel, QLineEdit, QPushButton, QTextEdit, QTableWidget,
        QTableWidgetItem, QComboBox, QSpinBox, QCheckBox, QGroupBox,
        QSplitter, QStatusBar, QMenuBar, QAction, QMessageBox, QDialog,
        QDialogButtonBox, QFormLayout, QDateTimeEdit, QProgressBar,
        QTreeWidget, QTreeWidgetItem, QHeaderView, QFrame
    )
    from PyQt5.QtCore import (
        Qt, QTimer, QThread, pyqtSignal, QDateTime, QSettings, QSize
    )
    from PyQt5.QtGui import QFont, QIcon, QPixmap, QPalette, QColor
except ImportError:
    print("请安装PyQt5: pip install PyQt5")
    sys.exit(1)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class QMTConnectionWidget(QWidget):
    """QMT连接配置和状态显示组件"""
    
    connection_changed = pyqtSignal(bool)  # 连接状态变化信号
    
    def __init__(self):
        super().__init__()
        self.qmt_data = None
        self.qmt_trader = None
        self.connection_status = False
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # QMT配置组
        config_group = QGroupBox("QMT连接配置")
        config_layout = QFormLayout()
        
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("QMT安装路径，如: D:\\国金证券QMT交易端\\userdata_mini")
        config_layout.addRow("QMT路径:", self.path_edit)
        
        self.session_edit = QLineEdit()
        self.session_edit.setPlaceholderText("会话ID，如: 123456")
        config_layout.addRow("会话ID:", self.session_edit)
        
        self.account_edit = QLineEdit()
        self.account_edit.setPlaceholderText("资金账号")
        config_layout.addRow("资金账号:", self.account_edit)
        
        self.token_edit = QLineEdit()
        self.token_edit.setPlaceholderText("访问令牌(可选)")
        config_layout.addRow("访问令牌:", self.token_edit)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # 连接控制组
        control_group = QGroupBox("连接控制")
        control_layout = QHBoxLayout()
        
        self.connect_btn = QPushButton("连接QMT")
        self.connect_btn.clicked.connect(self.connect_qmt)
        control_layout.addWidget(self.connect_btn)
        
        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.clicked.connect(self.disconnect_qmt)
        self.disconnect_btn.setEnabled(False)
        control_layout.addWidget(self.disconnect_btn)
        
        self.test_btn = QPushButton("测试连接")
        self.test_btn.clicked.connect(self.test_connection)
        control_layout.addWidget(self.test_btn)
        
        control_layout.addStretch()
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # 状态显示组
        status_group = QGroupBox("连接状态")
        status_layout = QFormLayout()
        
        self.status_label = QLabel("未连接")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        status_layout.addRow("连接状态:", self.status_label)
        
        self.data_status_label = QLabel("未连接")
        status_layout.addRow("数据连接:", self.data_status_label)
        
        self.trader_status_label = QLabel("未连接")
        status_layout.addRow("交易连接:", self.trader_status_label)
        
        self.last_update_label = QLabel("从未更新")
        status_layout.addRow("最后更新:", self.last_update_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # 定时器检查连接状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒检查一次
        
    def connect_qmt(self):
        """连接QMT"""
        try:
            # 保存配置
            self.save_settings()
            
            # 这里应该调用实际的QMT连接代码
            # 暂时模拟连接过程
            self.status_label.setText("连接中...")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            
            # 模拟连接成功
            QTimer.singleShot(2000, self.on_connection_success)
            
        except Exception as e:
            self.on_connection_error(str(e))
    
    def on_connection_success(self):
        """连接成功回调"""
        self.connection_status = True
        self.status_label.setText("已连接")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        self.data_status_label.setText("正常")
        self.trader_status_label.setText("正常")
        self.last_update_label.setText(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        self.connect_btn.setEnabled(False)
        self.disconnect_btn.setEnabled(True)
        
        self.connection_changed.emit(True)
        
    def on_connection_error(self, error_msg: str):
        """连接错误回调"""
        self.connection_status = False
        self.status_label.setText(f"连接失败: {error_msg}")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        
        QMessageBox.critical(self, "连接错误", f"QMT连接失败:\n{error_msg}")
        
    def disconnect_qmt(self):
        """断开QMT连接"""
        try:
            self.connection_status = False
            self.status_label.setText("未连接")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.data_status_label.setText("未连接")
            self.trader_status_label.setText("未连接")
            
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
            
            self.connection_changed.emit(False)
            
        except Exception as e:
            QMessageBox.critical(self, "断开错误", f"断开连接时出错:\n{str(e)}")
    
    def test_connection(self):
        """测试连接"""
        try:
            # 这里应该实现实际的连接测试
            # 暂时模拟测试过程
            QMessageBox.information(self, "连接测试", "连接测试成功!\n数据接口正常\n交易接口正常")
        except Exception as e:
            QMessageBox.critical(self, "测试失败", f"连接测试失败:\n{str(e)}")
    
    def update_status(self):
        """更新连接状态"""
        if self.connection_status:
            self.last_update_label.setText(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    def save_settings(self):
        """保存配置"""
        settings = QSettings("QuantTool", "QMTConnection")
        settings.setValue("qmt_path", self.path_edit.text())
        settings.setValue("session_id", self.session_edit.text())
        settings.setValue("account", self.account_edit.text())
        settings.setValue("token", self.token_edit.text())
    
    def load_settings(self):
        """加载配置"""
        settings = QSettings("QuantTool", "QMTConnection")
        self.path_edit.setText(settings.value("qmt_path", ""))
        self.session_edit.setText(settings.value("session_id", ""))
        self.account_edit.setText(settings.value("account", ""))
        self.token_edit.setText(settings.value("token", ""))


class CacheConfigWidget(QWidget):
    """缓存配置组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 缓存配置组
        cache_group = QGroupBox("缓存配置")
        cache_layout = QFormLayout()
        
        self.max_cache_spin = QSpinBox()
        self.max_cache_spin.setRange(1000, 100000)
        self.max_cache_spin.setValue(10000)
        self.max_cache_spin.setSuffix(" 条")
        cache_layout.addRow("最大缓存数量:", self.max_cache_spin)
        
        self.cache_timeout_spin = QSpinBox()
        self.cache_timeout_spin.setRange(1, 3600)
        self.cache_timeout_spin.setValue(300)
        self.cache_timeout_spin.setSuffix(" 秒")
        cache_layout.addRow("缓存超时时间:", self.cache_timeout_spin)
        
        self.auto_clear_check = QCheckBox("自动清理过期缓存")
        self.auto_clear_check.setChecked(True)
        cache_layout.addRow("", self.auto_clear_check)
        
        cache_group.setLayout(cache_layout)
        layout.addWidget(cache_group)
        
        # 缓存控制组
        control_group = QGroupBox("缓存控制")
        control_layout = QHBoxLayout()
        
        self.clear_cache_btn = QPushButton("清理缓存")
        self.clear_cache_btn.clicked.connect(self.clear_cache)
        control_layout.addWidget(self.clear_cache_btn)
        
        self.view_cache_btn = QPushButton("查看缓存")
        self.view_cache_btn.clicked.connect(self.view_cache)
        control_layout.addWidget(self.view_cache_btn)
        
        control_layout.addStretch()
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # 缓存状态组
        status_group = QGroupBox("缓存状态")
        status_layout = QFormLayout()
        
        self.cache_count_label = QLabel("0")
        status_layout.addRow("当前缓存数量:", self.cache_count_label)
        
        self.cache_size_label = QLabel("0 MB")
        status_layout.addRow("缓存大小:", self.cache_size_label)
        
        self.last_clear_label = QLabel("从未清理")
        status_layout.addRow("最后清理时间:", self.last_clear_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # 定时器更新缓存状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_cache_status)
        self.update_timer.start(10000)  # 每10秒更新一次
        
    def clear_cache(self):
        """清理缓存"""
        try:
            # 这里应该调用实际的缓存清理代码
            self.cache_count_label.setText("0")
            self.cache_size_label.setText("0 MB")
            self.last_clear_label.setText(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            
            QMessageBox.information(self, "清理完成", "缓存清理完成!")
            
        except Exception as e:
            QMessageBox.critical(self, "清理失败", f"缓存清理失败:\n{str(e)}")
    
    def view_cache(self):
        """查看缓存详情"""
        # 这里可以打开一个对话框显示缓存详情
        QMessageBox.information(self, "缓存详情", "缓存详情功能待实现")
    
    def update_cache_status(self):
        """更新缓存状态"""
        # 这里应该获取实际的缓存状态
        # 暂时显示模拟数据
        import random
        count = random.randint(1000, 5000)
        size = round(count * 0.001, 2)
        
        self.cache_count_label.setText(str(count))
        self.cache_size_label.setText(f"{size} MB")
    
    def save_settings(self):
        """保存配置"""
        settings = QSettings("QuantTool", "CacheConfig")
        settings.setValue("max_cache", self.max_cache_spin.value())
        settings.setValue("cache_timeout", self.cache_timeout_spin.value())
        settings.setValue("auto_clear", self.auto_clear_check.isChecked())
    
    def load_settings(self):
        """加载配置"""
        settings = QSettings("QuantTool", "CacheConfig")
        self.max_cache_spin.setValue(settings.value("max_cache", 10000, int))
        self.cache_timeout_spin.setValue(settings.value("cache_timeout", 300, int))
        self.auto_clear_check.setChecked(settings.value("auto_clear", True, bool))


class LogWidget(QWidget):
    """日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_logging()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 日志控制栏
        control_layout = QHBoxLayout()
        
        self.level_combo = QComboBox()
        self.level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.setCurrentText("INFO")
        control_layout.addWidget(QLabel("日志级别:"))
        control_layout.addWidget(self.level_combo)
        
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        control_layout.addWidget(self.auto_scroll_check)
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_logs)
        control_layout.addWidget(self.clear_log_btn)
        
        self.save_log_btn = QPushButton("保存日志")
        self.save_log_btn.clicked.connect(self.save_logs)
        control_layout.addWidget(self.save_log_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        self.setLayout(layout)
        
    def setup_logging(self):
        """设置日志处理器"""
        # 创建自定义日志处理器
        self.log_handler = GuiLogHandler(self)
        self.log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.log_handler.setFormatter(formatter)
        
        # 添加到根日志记录器
        logging.getLogger().addHandler(self.log_handler)
        
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        color_map = {
            "DEBUG": "gray",
            "INFO": "black", 
            "WARNING": "orange",
            "ERROR": "red",
            "CRITICAL": "darkred"
        }
        color = color_map.get(level, "black")
        
        formatted_msg = f'<span style="color: {color};">[{timestamp}] {level}: {message}</span>'
        self.log_text.append(formatted_msg)
        
        # 自动滚动到底部
        if self.auto_scroll_check.isChecked():
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
    
    def clear_logs(self):
        """清空日志"""
        self.log_text.clear()
        
    def save_logs(self):
        """保存日志到文件"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            filename, _ = QFileDialog.getSaveFileName(
                self, "保存日志", f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*)"
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "保存成功", f"日志已保存到:\n{filename}")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存日志失败:\n{str(e)}")


class GuiLogHandler(logging.Handler):
    """GUI日志处理器"""

    def __init__(self, log_widget):
        super().__init__()
        self.log_widget = log_widget

    def emit(self, record):
        try:
            msg = self.format(record)
            self.log_widget.add_log(msg, record.levelname)
        except Exception:
            self.handleError(record)


class IndicatorWidget(QWidget):
    """基础指标测试组件"""

    def __init__(self):
        super().__init__()
        self.indicators = {}
        self.init_ui()
        self.load_indicators()

    def init_ui(self):
        layout = QVBoxLayout()

        # 指标选择组
        select_group = QGroupBox("指标选择")
        select_layout = QHBoxLayout()

        self.indicator_combo = QComboBox()
        self.indicator_combo.currentTextChanged.connect(self.on_indicator_changed)
        select_layout.addWidget(QLabel("选择指标:"))
        select_layout.addWidget(self.indicator_combo)

        self.refresh_btn = QPushButton("刷新指标")
        self.refresh_btn.clicked.connect(self.load_indicators)
        select_layout.addWidget(self.refresh_btn)

        select_layout.addStretch()
        select_group.setLayout(select_layout)
        layout.addWidget(select_group)

        # 参数配置组
        self.param_group = QGroupBox("参数配置")
        self.param_layout = QFormLayout()
        self.param_group.setLayout(self.param_layout)
        layout.addWidget(self.param_group)

        # 执行控制组
        control_group = QGroupBox("执行控制")
        control_layout = QHBoxLayout()

        self.run_btn = QPushButton("运行指标")
        self.run_btn.clicked.connect(self.run_indicator)
        control_layout.addWidget(self.run_btn)

        self.validate_btn = QPushButton("验证参数")
        self.validate_btn.clicked.connect(self.validate_params)
        control_layout.addWidget(self.validate_btn)

        control_layout.addStretch()
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # 结果显示组
        result_group = QGroupBox("运行结果")
        result_layout = QVBoxLayout()

        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMaximumHeight(150)
        result_layout.addWidget(self.result_text)

        result_group.setLayout(result_layout)
        layout.addWidget(result_group)

        layout.addStretch()
        self.setLayout(layout)

    def load_indicators(self):
        """加载可用指标"""
        # 模拟指标列表
        self.indicators = {
            "移动平均线(MA)": {
                "function": "calculate_ma",
                "params": {
                    "data": {"type": "list", "description": "价格数据列表", "required": True},
                    "period": {"type": "int", "description": "周期", "default": 20, "required": True},
                    "ma_type": {"type": "str", "description": "MA类型(SMA/EMA)", "default": "SMA", "required": False}
                }
            },
            "相对强弱指标(RSI)": {
                "function": "calculate_rsi",
                "params": {
                    "data": {"type": "list", "description": "价格数据列表", "required": True},
                    "period": {"type": "int", "description": "周期", "default": 14, "required": True}
                }
            },
            "MACD指标": {
                "function": "calculate_macd",
                "params": {
                    "data": {"type": "list", "description": "价格数据列表", "required": True},
                    "fast_period": {"type": "int", "description": "快线周期", "default": 12, "required": True},
                    "slow_period": {"type": "int", "description": "慢线周期", "default": 26, "required": True},
                    "signal_period": {"type": "int", "description": "信号线周期", "default": 9, "required": True}
                }
            },
            "布林带(Bollinger)": {
                "function": "calculate_bollinger",
                "params": {
                    "data": {"type": "list", "description": "价格数据列表", "required": True},
                    "period": {"type": "int", "description": "周期", "default": 20, "required": True},
                    "std_dev": {"type": "float", "description": "标准差倍数", "default": 2.0, "required": True}
                }
            }
        }

        self.indicator_combo.clear()
        self.indicator_combo.addItems(list(self.indicators.keys()))

    def on_indicator_changed(self, indicator_name: str):
        """指标选择变化"""
        if not indicator_name or indicator_name not in self.indicators:
            return

        # 清空之前的参数控件
        for i in reversed(range(self.param_layout.count())):
            child = self.param_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 创建新的参数控件
        indicator_info = self.indicators[indicator_name]
        params = indicator_info["params"]

        self.param_widgets = {}

        for param_name, param_info in params.items():
            param_type = param_info["type"]
            description = param_info["description"]
            required = param_info.get("required", False)
            default = param_info.get("default", "")

            label_text = f"{param_name}"
            if required:
                label_text += " *"
            label_text += f" ({param_type})"

            label = QLabel(label_text)
            label.setToolTip(description)

            if param_type == "int":
                widget = QSpinBox()
                widget.setRange(-999999, 999999)
                if default:
                    widget.setValue(int(default))
            elif param_type == "float":
                widget = QLineEdit()
                if default:
                    widget.setText(str(default))
                widget.setPlaceholderText("输入浮点数")
            elif param_type == "str":
                widget = QLineEdit()
                if default:
                    widget.setText(str(default))
                widget.setPlaceholderText("输入字符串")
            elif param_type == "list":
                widget = QLineEdit()
                widget.setPlaceholderText("输入列表，如: [1,2,3,4,5] 或使用测试数据")
            else:
                widget = QLineEdit()
                if default:
                    widget.setText(str(default))

            widget.setToolTip(description)
            self.param_widgets[param_name] = widget
            self.param_layout.addRow(label, widget)

        # 添加说明
        info_label = QLabel(f"函数: {indicator_info['function']}")
        info_label.setStyleSheet("color: gray; font-style: italic;")
        self.param_layout.addRow("", info_label)

    def validate_params(self):
        """验证参数"""
        try:
            indicator_name = self.indicator_combo.currentText()
            if not indicator_name:
                QMessageBox.warning(self, "警告", "请先选择指标")
                return

            params = self.get_current_params()
            if params is None:
                return

            # 验证参数
            indicator_info = self.indicators[indicator_name]
            required_params = [name for name, info in indicator_info["params"].items()
                             if info.get("required", False)]

            missing_params = []
            for param in required_params:
                if param not in params or not params[param]:
                    missing_params.append(param)

            if missing_params:
                QMessageBox.warning(self, "参数错误", f"缺少必需参数: {', '.join(missing_params)}")
                return

            QMessageBox.information(self, "验证成功", "参数验证通过!")

        except Exception as e:
            QMessageBox.critical(self, "验证失败", f"参数验证失败:\n{str(e)}")

    def get_current_params(self) -> Optional[Dict[str, Any]]:
        """获取当前参数值"""
        try:
            params = {}
            for param_name, widget in self.param_widgets.items():
                if isinstance(widget, QSpinBox):
                    params[param_name] = widget.value()
                elif isinstance(widget, QLineEdit):
                    text = widget.text().strip()
                    if not text:
                        params[param_name] = None
                        continue

                    # 尝试解析不同类型的值
                    if text.startswith('[') and text.endswith(']'):
                        # 列表类型
                        try:
                            params[param_name] = eval(text)
                        except:
                            # 如果解析失败，使用测试数据
                            params[param_name] = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
                    else:
                        # 尝试转换为数字
                        try:
                            if '.' in text:
                                params[param_name] = float(text)
                            else:
                                params[param_name] = int(text)
                        except ValueError:
                            params[param_name] = text

            return params

        except Exception as e:
            QMessageBox.critical(self, "参数错误", f"获取参数失败:\n{str(e)}")
            return None

    def run_indicator(self):
        """运行指标"""
        try:
            indicator_name = self.indicator_combo.currentText()
            if not indicator_name:
                QMessageBox.warning(self, "警告", "请先选择指标")
                return

            params = self.get_current_params()
            if params is None:
                return

            # 模拟运行指标
            indicator_info = self.indicators[indicator_name]
            function_name = indicator_info["function"]

            # 这里应该调用实际的指标计算函数
            # 暂时模拟结果
            import random
            result = {
                "function": function_name,
                "params": params,
                "result": [round(random.uniform(10, 20), 2) for _ in range(10)],
                "execution_time": f"{random.uniform(0.1, 2.0):.3f}s"
            }

            # 显示结果
            result_text = f"指标: {indicator_name}\n"
            result_text += f"函数: {function_name}\n"
            result_text += f"参数: {params}\n"
            result_text += f"结果: {result['result']}\n"
            result_text += f"执行时间: {result['execution_time']}\n"
            result_text += f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            self.result_text.setText(result_text)

            logger.info(f"指标运行完成: {indicator_name}")

        except Exception as e:
            error_msg = f"指标运行失败:\n{str(e)}"
            self.result_text.setText(error_msg)
            QMessageBox.critical(self, "运行失败", error_msg)
            logger.error(f"指标运行失败: {str(e)}")


class StrategyManagerWidget(QWidget):
    """策略管理组件"""

    def __init__(self):
        super().__init__()
        self.strategies = {}
        self.running_strategies = {}
        self.init_ui()
        self.load_strategies()

    def init_ui(self):
        layout = QVBoxLayout()

        # 策略列表组
        list_group = QGroupBox("策略列表")
        list_layout = QVBoxLayout()

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.add_strategy_btn = QPushButton("添加策略")
        self.add_strategy_btn.clicked.connect(self.add_strategy)
        toolbar_layout.addWidget(self.add_strategy_btn)

        self.edit_strategy_btn = QPushButton("编辑策略")
        self.edit_strategy_btn.clicked.connect(self.edit_strategy)
        toolbar_layout.addWidget(self.edit_strategy_btn)

        self.delete_strategy_btn = QPushButton("删除策略")
        self.delete_strategy_btn.clicked.connect(self.delete_strategy)
        toolbar_layout.addWidget(self.delete_strategy_btn)

        toolbar_layout.addStretch()
        list_layout.addLayout(toolbar_layout)

        # 策略表格
        self.strategy_table = QTableWidget()
        self.strategy_table.setColumnCount(6)
        self.strategy_table.setHorizontalHeaderLabels([
            "策略名称", "状态", "运行模式", "开始时间", "结束时间", "最后运行"
        ])
        self.strategy_table.horizontalHeader().setStretchLastSection(True)
        self.strategy_table.setSelectionBehavior(QTableWidget.SelectRows)
        list_layout.addWidget(self.strategy_table)

        list_group.setLayout(list_layout)
        layout.addWidget(list_group)

        # 策略控制组
        control_group = QGroupBox("策略控制")
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("启动策略")
        self.start_btn.clicked.connect(self.start_strategy)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止策略")
        self.stop_btn.clicked.connect(self.stop_strategy)
        control_layout.addWidget(self.stop_btn)

        self.schedule_btn = QPushButton("定时运行")
        self.schedule_btn.clicked.connect(self.schedule_strategy)
        control_layout.addWidget(self.schedule_btn)

        control_layout.addStretch()

        self.refresh_btn = QPushButton("刷新状态")
        self.refresh_btn.clicked.connect(self.refresh_strategies)
        control_layout.addWidget(self.refresh_btn)

        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # 策略详情组
        detail_group = QGroupBox("策略详情")
        detail_layout = QVBoxLayout()

        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setMaximumHeight(120)
        detail_layout.addWidget(self.detail_text)

        detail_group.setLayout(detail_layout)
        layout.addWidget(detail_group)

        self.setLayout(layout)

        # 定时器更新策略状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_strategy_status)
        self.update_timer.start(5000)  # 每5秒更新一次

        # 连接表格选择事件
        self.strategy_table.itemSelectionChanged.connect(self.on_strategy_selected)

    def load_strategies(self):
        """加载策略列表"""
        # 模拟策略数据
        self.strategies = {
            "跟风买入策略": {
                "file": "strategy/跟风买入策略.py",
                "class": "跟风买入策略",
                "status": "stopped",
                "mode": "manual",
                "start_time": None,
                "end_time": None,
                "last_run": None,
                "description": "基于市场情绪的跟风买入策略"
            },
            "均线策略": {
                "file": "strategy/ma_strategy.py",
                "class": "MAStrategy",
                "status": "running",
                "mode": "scheduled",
                "start_time": "09:30:00",
                "end_time": "15:00:00",
                "last_run": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "description": "基于移动平均线的交易策略"
            }
        }

        self.refresh_strategies()

    def refresh_strategies(self):
        """刷新策略表格"""
        self.strategy_table.setRowCount(len(self.strategies))

        for row, (name, info) in enumerate(self.strategies.items()):
            self.strategy_table.setItem(row, 0, QTableWidgetItem(name))

            # 状态显示
            status_item = QTableWidgetItem(info["status"])
            if info["status"] == "running":
                status_item.setBackground(QColor(144, 238, 144))  # 浅绿色
            elif info["status"] == "stopped":
                status_item.setBackground(QColor(255, 182, 193))  # 浅红色
            elif info["status"] == "scheduled":
                status_item.setBackground(QColor(255, 255, 224))  # 浅黄色
            self.strategy_table.setItem(row, 1, status_item)

            self.strategy_table.setItem(row, 2, QTableWidgetItem(info["mode"]))
            self.strategy_table.setItem(row, 3, QTableWidgetItem(info["start_time"] or ""))
            self.strategy_table.setItem(row, 4, QTableWidgetItem(info["end_time"] or ""))
            self.strategy_table.setItem(row, 5, QTableWidgetItem(info["last_run"] or ""))

    def on_strategy_selected(self):
        """策略选择事件"""
        current_row = self.strategy_table.currentRow()
        if current_row >= 0:
            strategy_name = self.strategy_table.item(current_row, 0).text()
            if strategy_name in self.strategies:
                info = self.strategies[strategy_name]
                detail_text = f"策略名称: {strategy_name}\n"
                detail_text += f"文件路径: {info['file']}\n"
                detail_text += f"策略类: {info['class']}\n"
                detail_text += f"运行状态: {info['status']}\n"
                detail_text += f"运行模式: {info['mode']}\n"
                detail_text += f"描述: {info['description']}\n"

                self.detail_text.setText(detail_text)

    def add_strategy(self):
        """添加策略"""
        dialog = StrategyDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            strategy_info = dialog.get_strategy_info()
            self.strategies[strategy_info["name"]] = strategy_info
            self.refresh_strategies()
            logger.info(f"添加策略: {strategy_info['name']}")

    def edit_strategy(self):
        """编辑策略"""
        current_row = self.strategy_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的策略")
            return

        strategy_name = self.strategy_table.item(current_row, 0).text()
        strategy_info = self.strategies[strategy_name]

        dialog = StrategyDialog(self, strategy_info)
        if dialog.exec_() == QDialog.Accepted:
            new_info = dialog.get_strategy_info()
            # 如果名称改变了，需要删除旧的
            if new_info["name"] != strategy_name:
                del self.strategies[strategy_name]
            self.strategies[new_info["name"]] = new_info
            self.refresh_strategies()
            logger.info(f"编辑策略: {new_info['name']}")

    def delete_strategy(self):
        """删除策略"""
        current_row = self.strategy_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的策略")
            return

        strategy_name = self.strategy_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除策略 '{strategy_name}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 如果策略正在运行，先停止
            if self.strategies[strategy_name]["status"] == "running":
                self.stop_strategy_by_name(strategy_name)

            del self.strategies[strategy_name]
            self.refresh_strategies()
            self.detail_text.clear()
            logger.info(f"删除策略: {strategy_name}")

    def start_strategy(self):
        """启动策略"""
        current_row = self.strategy_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要启动的策略")
            return

        strategy_name = self.strategy_table.item(current_row, 0).text()

        try:
            # 这里应该调用实际的策略启动代码
            self.strategies[strategy_name]["status"] = "running"
            self.strategies[strategy_name]["last_run"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            self.refresh_strategies()
            logger.info(f"启动策略: {strategy_name}")
            QMessageBox.information(self, "成功", f"策略 '{strategy_name}' 启动成功")

        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"策略启动失败:\n{str(e)}")
            logger.error(f"策略启动失败: {str(e)}")

    def stop_strategy(self):
        """停止策略"""
        current_row = self.strategy_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要停止的策略")
            return

        strategy_name = self.strategy_table.item(current_row, 0).text()
        self.stop_strategy_by_name(strategy_name)

    def stop_strategy_by_name(self, strategy_name: str):
        """根据名称停止策略"""
        try:
            # 这里应该调用实际的策略停止代码
            self.strategies[strategy_name]["status"] = "stopped"

            self.refresh_strategies()
            logger.info(f"停止策略: {strategy_name}")

        except Exception as e:
            QMessageBox.critical(self, "停止失败", f"策略停止失败:\n{str(e)}")
            logger.error(f"策略停止失败: {str(e)}")

    def schedule_strategy(self):
        """定时运行策略"""
        current_row = self.strategy_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要定时运行的策略")
            return

        strategy_name = self.strategy_table.item(current_row, 0).text()

        dialog = ScheduleDialog(self, self.strategies[strategy_name])
        if dialog.exec_() == QDialog.Accepted:
            schedule_info = dialog.get_schedule_info()
            self.strategies[strategy_name].update(schedule_info)
            self.strategies[strategy_name]["status"] = "scheduled"
            self.refresh_strategies()
            logger.info(f"设置定时运行: {strategy_name}")

    def update_strategy_status(self):
        """更新策略运行状态"""
        # 这里应该检查实际的策略运行状态
        # 暂时模拟状态更新
        for name, info in self.strategies.items():
            if info["status"] == "running":
                info["last_run"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.refresh_strategies()


class StrategyDialog(QDialog):
    """策略配置对话框"""

    def __init__(self, parent=None, strategy_info=None):
        super().__init__(parent)
        self.strategy_info = strategy_info or {}
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("策略配置")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout()

        # 基本信息
        form_layout = QFormLayout()

        self.name_edit = QLineEdit()
        self.name_edit.setText(self.strategy_info.get("name", ""))
        form_layout.addRow("策略名称:", self.name_edit)

        self.file_edit = QLineEdit()
        self.file_edit.setText(self.strategy_info.get("file", ""))
        form_layout.addRow("文件路径:", self.file_edit)

        self.class_edit = QLineEdit()
        self.class_edit.setText(self.strategy_info.get("class", ""))
        form_layout.addRow("策略类名:", self.class_edit)

        self.desc_edit = QTextEdit()
        self.desc_edit.setText(self.strategy_info.get("description", ""))
        self.desc_edit.setMaximumHeight(80)
        form_layout.addRow("策略描述:", self.desc_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            "name": self.name_edit.text().strip(),
            "file": self.file_edit.text().strip(),
            "class": self.class_edit.text().strip(),
            "description": self.desc_edit.toPlainText().strip(),
            "status": self.strategy_info.get("status", "stopped"),
            "mode": self.strategy_info.get("mode", "manual"),
            "start_time": self.strategy_info.get("start_time"),
            "end_time": self.strategy_info.get("end_time"),
            "last_run": self.strategy_info.get("last_run")
        }


class ScheduleDialog(QDialog):
    """定时任务配置对话框"""

    def __init__(self, parent=None, strategy_info=None):
        super().__init__(parent)
        self.strategy_info = strategy_info or {}
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("定时运行配置")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout()

        # 运行模式
        mode_group = QGroupBox("运行模式")
        mode_layout = QVBoxLayout()

        self.manual_radio = QCheckBox("手动运行")
        self.scheduled_radio = QCheckBox("定时运行")
        self.time_range_radio = QCheckBox("时间段运行")

        mode_layout.addWidget(self.manual_radio)
        mode_layout.addWidget(self.scheduled_radio)
        mode_layout.addWidget(self.time_range_radio)
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)

        # 时间配置
        time_group = QGroupBox("时间配置")
        time_layout = QFormLayout()

        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.start_time_edit.setDateTime(QDateTime.currentDateTime())
        time_layout.addRow("开始时间:", self.start_time_edit)

        self.end_time_edit = QDateTimeEdit()
        self.end_time_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.end_time_edit.setDateTime(QDateTime.currentDateTime().addSecs(3600))
        time_layout.addRow("结束时间:", self.end_time_edit)

        time_group.setLayout(time_layout)
        layout.addWidget(time_group)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def get_schedule_info(self) -> Dict[str, Any]:
        """获取定时配置信息"""
        mode = "manual"
        if self.scheduled_radio.isChecked():
            mode = "scheduled"
        elif self.time_range_radio.isChecked():
            mode = "time_range"

        return {
            "mode": mode,
            "start_time": self.start_time_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss"),
            "end_time": self.end_time_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
        }


class QuantTradingMainWindow(QMainWindow):
    """量化交易系统主窗口"""

    def __init__(self):
        super().__init__()
        self.qmt_widget = None
        self.cache_widget = None
        self.strategy_widget = None
        self.indicator_widget = None
        self.log_widget = None

        self.init_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.connect_signals()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("量化交易系统管理工具 v1.0")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("icon.png"))

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局 - 使用分割器
        main_splitter = QSplitter(Qt.Horizontal)

        # 左侧：连接和配置面板
        left_widget = QWidget()
        left_layout = QVBoxLayout()

        # QMT连接组件
        self.qmt_widget = QMTConnectionWidget()
        left_layout.addWidget(self.qmt_widget)

        # 缓存配置组件
        self.cache_widget = CacheConfigWidget()
        left_layout.addWidget(self.cache_widget)

        left_widget.setLayout(left_layout)
        left_widget.setMaximumWidth(400)
        main_splitter.addWidget(left_widget)

        # 右侧：主要功能区域
        right_widget = QTabWidget()

        # 策略管理标签页
        self.strategy_widget = StrategyManagerWidget()
        right_widget.addTab(self.strategy_widget, "策略管理")

        # 指标测试标签页
        self.indicator_widget = IndicatorWidget()
        right_widget.addTab(self.indicator_widget, "指标测试")

        # 运行日志标签页
        self.log_widget = LogWidget()
        right_widget.addTab(self.log_widget, "运行日志")

        main_splitter.addWidget(right_widget)

        # 设置分割器比例
        main_splitter.setSizes([350, 1050])

        # 主布局
        layout = QVBoxLayout()
        layout.addWidget(main_splitter)
        central_widget.setLayout(layout)

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 导入策略
        import_action = QAction('导入策略...', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_strategy)
        file_menu.addAction(import_action)

        # 导出策略
        export_action = QAction('导出策略...', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_strategy)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')

        # 清理缓存
        clear_cache_action = QAction('清理缓存', self)
        clear_cache_action.triggered.connect(self.clear_all_cache)
        tools_menu.addAction(clear_cache_action)

        # 重新连接QMT
        reconnect_action = QAction('重新连接QMT', self)
        reconnect_action.triggered.connect(self.reconnect_qmt)
        tools_menu.addAction(reconnect_action)

        tools_menu.addSeparator()

        # 系统设置
        settings_action = QAction('系统设置...', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于...', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()

        # QMT连接状态
        self.qmt_status_label = QLabel("QMT: 未连接")
        self.qmt_status_label.setStyleSheet("color: red;")
        self.status_bar.addWidget(self.qmt_status_label)

        self.status_bar.addWidget(QLabel(" | "))

        # 运行策略数量
        self.strategy_status_label = QLabel("运行策略: 0")
        self.status_bar.addWidget(self.strategy_status_label)

        self.status_bar.addWidget(QLabel(" | "))

        # 缓存状态
        self.cache_status_label = QLabel("缓存: 0 条")
        self.status_bar.addWidget(self.cache_status_label)

        # 右侧显示时间
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)

        # 定时器更新时间和状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(1000)  # 每秒更新

    def connect_signals(self):
        """连接信号"""
        # QMT连接状态变化
        if self.qmt_widget:
            self.qmt_widget.connection_changed.connect(self.on_qmt_connection_changed)

    def update_status_bar(self):
        """更新状态栏"""
        # 更新时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

        # 更新策略状态
        if self.strategy_widget:
            running_count = sum(1 for info in self.strategy_widget.strategies.values()
                              if info["status"] == "running")
            self.strategy_status_label.setText(f"运行策略: {running_count}")

    def on_qmt_connection_changed(self, connected: bool):
        """QMT连接状态变化"""
        if connected:
            self.qmt_status_label.setText("QMT: 已连接")
            self.qmt_status_label.setStyleSheet("color: green;")
        else:
            self.qmt_status_label.setText("QMT: 未连接")
            self.qmt_status_label.setStyleSheet("color: red;")

    def import_strategy(self):
        """导入策略"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            filename, _ = QFileDialog.getOpenFileName(
                self, "导入策略文件", "", "Python文件 (*.py);;所有文件 (*)"
            )
            if filename:
                # 这里应该实现策略文件的导入逻辑
                QMessageBox.information(self, "导入成功", f"策略文件导入成功:\n{filename}")
                logger.info(f"导入策略文件: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "导入失败", f"策略导入失败:\n{str(e)}")

    def export_strategy(self):
        """导出策略"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出策略配置", "strategies.json", "JSON文件 (*.json);;所有文件 (*)"
            )
            if filename:
                # 导出策略配置
                if self.strategy_widget:
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(self.strategy_widget.strategies, f,
                                ensure_ascii=False, indent=2)
                    QMessageBox.information(self, "导出成功", f"策略配置导出成功:\n{filename}")
                    logger.info(f"导出策略配置: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"策略导出失败:\n{str(e)}")

    def clear_all_cache(self):
        """清理所有缓存"""
        if self.cache_widget:
            self.cache_widget.clear_cache()

    def reconnect_qmt(self):
        """重新连接QMT"""
        if self.qmt_widget:
            self.qmt_widget.disconnect_qmt()
            QTimer.singleShot(1000, self.qmt_widget.connect_qmt)

    def show_settings(self):
        """显示系统设置"""
        QMessageBox.information(self, "系统设置", "系统设置功能待实现")

    def show_about(self):
        """显示关于信息"""
        about_text = """
        <h3>量化交易系统管理工具 v1.0</h3>
        <p>基于PyQt5开发的专业量化交易管理工具</p>
        <p><b>主要功能:</b></p>
        <ul>
        <li>QMT连接管理</li>
        <li>策略管理和调度</li>
        <li>指标测试和验证</li>
        <li>缓存管理</li>
        <li>运行日志监控</li>
        </ul>
        <p><b>开发:</b> 量化交易团队</p>
        <p><b>版本:</b> 1.0.0</p>
        """
        QMessageBox.about(self, "关于", about_text)

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self, "确认退出",
            "确定要退出量化交易系统吗？\n正在运行的策略将被停止。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 保存配置
            if self.qmt_widget:
                self.qmt_widget.save_settings()
            if self.cache_widget:
                self.cache_widget.save_settings()

            # 停止所有运行中的策略
            if self.strategy_widget:
                for name, info in self.strategy_widget.strategies.items():
                    if info["status"] == "running":
                        self.strategy_widget.stop_strategy_by_name(name)

            logger.info("量化交易系统正常退出")
            event.accept()
        else:
            event.ignore()


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("量化交易系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("QuantTrading")

    # 设置应用样式
    app.setStyle('Fusion')

    # 设置深色主题（可选）
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    palette.setColor(QPalette.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
    palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
    # app.setPalette(palette)  # 取消注释以启用深色主题

    # 创建主窗口
    window = QuantTradingMainWindow()
    window.show()

    # 启动日志
    logger.info("量化交易系统GUI启动成功")
    logger.info("系统初始化完成，等待用户操作...")

    sys.exit(app.exec_())
