
# 统计信息保存
from core.qmt_data import QmtData
from core.tsl_data import TslData
import pandas as pd
import numpy as np
import time
from core.utility import get_stock_ratio
class SaveInfo:
    def __init__(self) -> None:
        self.qmt_data = QmtData()
        # self.tsl_client = TslData()
        self.stock_list = self.qmt_data.get_vt_symbols()

    def get_today_base_info(self):
        full_tick = self.qmt_data.get_full_tick(self.stock_list)
        data = []
        for k, v in full_tick.items():
            v["stock_code"] = k
            v["limit_ratio"] = get_stock_ratio(k)
            data.append(v)
        df = pd.DataFrame.from_dict(data, orient='index')
        return df
    
    def download_history_data(self):
        count = 0
        def on_data(data):
            count += 1
            print(data)
        print(self.stock_list[:3], len(self.stock_list))
        start_time = "20250820"
        self.qmt_data.download_history_data2(
            stock_list=self.stock_list,
            period="1d",
            start_time=start_time,
            # freq="day",
            callback=on_data
        )
        while True:
            time.sleep(3)
            print(count)

    def save_info(self):
        df = self.get_today_base_info()
        df["limit_up_price"] = df["pre_close"] * (1 + df["limit_ratio"]).round(2)
        df["limit_down_price"] = df["pre_close"] * (1 - df["limit_ratio"]).round(2)
        df.to_csv("today_base_info.csv", index=False)

    def get_his_data(self):
        data = self.qmt_data.get_market_data_ex(
            stock_list=self.stock_list[:3],
            period="1d",
            count=10
        )
        infos = []
        for k, df in data.items():
            print(df.iloc[-1].to_dict())
            df["股票代码"] = k
            limit_ratio = get_stock_ratio(k)
            df["最大涨幅"] = limit_ratio
            df["涨停价"] = (df["preClose"] * (1 + limit_ratio)).round(2)
            df["跌停价"] = (df["preClose"] * (1 - limit_ratio)).round(2)
            df["涨跌停"] = df.apply(lambda x: "涨停" if round(x["close"], 2) == round(x["涨停价"], 2) else "跌停" if round(x["close"], 2) == round(x["跌停价"], 2) else "", axis=1)
            df["是否是一字板"] = (df["high"] == df["low"])
            df.rename(columns={"close": "收盘价", "open": "开盘价", "high": "最高价", "low": "最低价", "preClose": "昨收价", "volume": "成交量", "amount": "成交额"}, inplace=True)
            infos.append(df)
        return infos       
    def save_his_data(self):
        """
        # 指标30:昨日最大一字连板天数个股  开盘及涨停 无炸板(第一天也是)
        # 指标30A，昨日一字板   个股代码和数量
        # 指标30B，前一日一字板 个股代码和数量
        # 指标30C，昨日一字板前日最高价小于前日涨停价  个股代码和数量
        # 指标30D，一字起板：第一个涨停板为一字板的个股。 个股代码和数量
        统计这些指标所需要的信息
        """
        # 获取更多天数的历史数据以便计算连板天数
        data = self.qmt_data.get_market_data_ex(
            stock_list=self.stock_list,
            period="1d",
            count=30  # 获取30天数据用于计算连板天数
        )

        # 存储各个指标的结果
        results = {
            "指标30_最大一字连板天数": {"个股": [], "最大天数": 0},
            "指标30A_昨日一字板": {"个股": [], "数量": 0},
            "指标30B_前一日一字板": {"个股": [], "数量": 0},
            "指标30C_昨日一字板且前日最高价小于前日涨停价": {"个股": [], "数量": 0},
            "指标30D_一字起板": {"个股": [], "数量": 0}
        }

        for stock_code, df in data.items():
            if df.empty or len(df) < 3:
                continue

            # 添加必要的计算字段
            limit_ratio = get_stock_ratio(stock_code)
            df["股票代码"] = stock_code
            df["最大涨幅"] = limit_ratio
            df["涨停价"] = (df["preClose"] * (1 + limit_ratio)).round(2)
            df["跌停价"] = (df["preClose"] * (1 - limit_ratio)).round(2)

            # 判断是否涨停
            df["是否涨停"] = df.apply(lambda x: round(x["close"], 2) == round(x["涨停价"], 2), axis=1)
            # 判断是否一字板（开盘价=最高价=最低价=收盘价，且涨停）
            df["是否一字板"] = df["是否涨停"] & (df["open"] == df["high"]) & (df["high"] == df["low"]) & (df["low"] == df["close"])

            # 按时间排序（最新的在最后）
            df = df.sort_index()

            # 计算各个指标
            self._calculate_indicators(stock_code, df, results)

        # 保存结果
        self._save_indicator_results(results)
        return results

    def _calculate_indicators(self, stock_code, df, results):
        """计算单个股票的各项指标"""
        if len(df) < 3:
            return

        # 获取最近几天的数据
        latest_day = df.iloc[-1]  # 昨日数据
        prev_day = df.iloc[-2] if len(df) >= 2 else None  # 前一日数据

        # 指标30A: 昨日一字板
        if latest_day["是否一字板"]:
            results["指标30A_昨日一字板"]["个股"].append(stock_code)
            results["指标30A_昨日一字板"]["数量"] += 1

        # 指标30B: 前一日一字板
        if prev_day is not None and prev_day["是否一字板"]:
            results["指标30B_前一日一字板"]["个股"].append(stock_code)
            results["指标30B_前一日一字板"]["数量"] += 1

        # 指标30C: 昨日一字板且前日最高价小于前日涨停价
        if (latest_day["是否一字板"] and prev_day is not None and
            round(prev_day["high"], 2) < round(prev_day["涨停价"], 2)):
            results["指标30C_昨日一字板且前日最高价小于前日涨停价"]["个股"].append(stock_code)
            results["指标30C_昨日一字板且前日最高价小于前日涨停价"]["数量"] += 1

        # 指标30D: 一字起板（第一个涨停板为一字板）
        first_limit_up_is_yizi = self._check_first_limit_up_is_yizi(df)
        if first_limit_up_is_yizi:
            results["指标30D_一字起板"]["个股"].append(stock_code)
            results["指标30D_一字起板"]["数量"] += 1

        # 指标30: 计算最大一字连板天数
        max_consecutive_days = self._calculate_max_consecutive_yizi_days(df)
        if max_consecutive_days > results["指标30_最大一字连板天数"]["最大天数"]:
            results["指标30_最大一字连板天数"]["最大天数"] = max_consecutive_days
            results["指标30_最大一字连板天数"]["个股"] = [stock_code]
        elif max_consecutive_days == results["指标30_最大一字连板天数"]["最大天数"] and max_consecutive_days > 0:
            results["指标30_最大一字连板天数"]["个股"].append(stock_code)

    def _check_first_limit_up_is_yizi(self, df):
        """检查第一个涨停板是否为一字板"""
        # 找到第一个涨停的日期
        limit_up_days = df[df["是否涨停"]]
        if limit_up_days.empty:
            return False

        first_limit_up = limit_up_days.iloc[0]
        return first_limit_up["是否一字板"]

    def _calculate_max_consecutive_yizi_days(self, df):
        """计算最大连续一字板天数"""
        if df.empty:
            return 0

        # 获取一字板标记
        yizi_flags = df["是否一字板"].values

        max_consecutive = 0
        current_consecutive = 0

        # 从最新日期往前计算连续天数
        for i in range(len(yizi_flags) - 1, -1, -1):
            if yizi_flags[i]:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _save_indicator_results(self, results):
        """保存指标结果到文件"""
        import json
        from datetime import datetime

        # 添加时间戳
        results["统计时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存为JSON文件
        with open("indicator_30_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 打印结果摘要
        print("\n=== 指标30系列统计结果 ===")
        print(f"指标30 - 最大一字连板天数: {results['指标30_最大一字连板天数']['最大天数']}天")
        print(f"         对应个股数量: {len(results['指标30_最大一字连板天数']['个股'])}个")
        print(f"指标30A - 昨日一字板个股数量: {results['指标30A_昨日一字板']['数量']}个")
        print(f"指标30B - 前一日一字板个股数量: {results['指标30B_前一日一字板']['数量']}个")
        print(f"指标30C - 昨日一字板且前日最高价小于前日涨停价: {results['指标30C_昨日一字板且前日最高价小于前日涨停价']['数量']}个")
        print(f"指标30D - 一字起板个股数量: {results['指标30D_一字起板']['数量']}个")
        print(f"\n详细结果已保存到: indicator_30_results.json")

        
if __name__ == "__main__":
    save_info = SaveInfo()
    # 运行指标30系列统计
    results = save_info.save_his_data()
    print("统计完成！")