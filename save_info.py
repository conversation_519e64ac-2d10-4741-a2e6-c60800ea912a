
# 统计信息保存
from core.qmt_data import QmtData
from core.tsl_data import TslData
import pandas as pd
import numpy as np
import time
from core.utility import get_stock_ratio
class SaveInfo:
    def __init__(self) -> None:
        self.qmt_data = QmtData()
        # self.tsl_client = TslData()
        self.stock_list = self.qmt_data.get_vt_symbols()

    def get_today_base_info(self):
        full_tick = self.qmt_data.get_full_tick(self.stock_list)
        data = []
        for k, v in full_tick.items():
            v["stock_code"] = k
            v["limit_ratio"] = get_stock_ratio(k)
            data.append(v)
        df = pd.DataFrame.from_dict(data, orient='index')
        return df
    
    def download_history_data(self):
        count = 0
        def on_data(data):
            count += 1
            print(data)
        print(self.stock_list[:3], len(self.stock_list))
        start_time = "20250820"
        self.qmt_data.download_history_data2(
            stock_list=self.stock_list,
            period="1d",
            start_time=start_time,
            # freq="day",
            callback=on_data
        )
        while True:
            time.sleep(3)
            print(count)

    def save_info(self):
        df = self.get_today_base_info()
        df["limit_up_price"] = df["pre_close"] * (1 + df["limit_ratio"]).round(2)
        df["limit_down_price"] = df["pre_close"] * (1 - df["limit_ratio"]).round(2)
        df.to_csv("today_base_info.csv", index=False)

    def get_his_data(self):
        data = self.qmt_data.get_market_data_ex(
            stock_list=self.stock_list[:3],
            period="1d",
            count=10
        )
        infos = []
        for k, df in data.items():
            print(df.iloc[-1].to_dict())
            df["股票代码"] = k
            limit_ratio = get_stock_ratio(k)
            df["最大涨幅"] = limit_ratio
            df["涨停价"] = (df["preClose"] * (1 + limit_ratio)).round(2)
            df["跌停价"] = (df["preClose"] * (1 - limit_ratio)).round(2)
            df["涨跌停"] = df.apply(lambda x: "涨停" if round(x["close"], 2) == round(x["涨停价"], 2) else "跌停" if round(x["close"], 2) == round(x["跌停价"], 2) else "", axis=1)
            df["是否是一字板"] = (df["high"] == df["low"])
            df.rename(columns={"close": "收盘价", "open": "开盘价", "high": "最高价", "low": "最低价", "preClose": "昨收价", "volume": "成交量", "amount": "成交额"}, inplace=True)
            infos.append(df)
        return infos       


if __name__ == "__main__":
    save_info = SaveInfo()
    save_info.get_his_data()