
# 统计信息保存
from core.qmt_data import QmtData
from core.tsl_data import TslData
import pandas as pd
import numpy as np
import time
from core.utility import get_stock_ratio
class SaveInfo:
    def __init__(self) -> None:
        self.qmt_data = QmtData()
        # self.tsl_client = TslData()
        self.stock_list = self.qmt_data.get_vt_symbols()

    def get_today_base_info(self):
        full_tick = self.qmt_data.get_full_tick(self.stock_list)
        data = []
        for k, v in full_tick.items():
            v["stock_code"] = k
            v["limit_ratio"] = get_stock_ratio(k)
            data.append(v)
        df = pd.DataFrame.from_dict(data, orient='index')
        return df
    
    def download_history_data(self):
        count = 0
        def on_data(data):
            count += 1
            print(data)
        print(self.stock_list[:3], len(self.stock_list))
        start_time = "20250820"
        self.qmt_data.download_history_data2(
            stock_list=self.stock_list,
            period="1d",
            start_time=start_time,
            # freq="day",
            callback=on_data
        )
        while True:
            time.sleep(3)
            print(count)

    def save_info(self):
        df = self.get_today_base_info()
        df["limit_up_price"] = df["pre_close"] * (1 + df["limit_ratio"]).round(2)
        df["limit_down_price"] = df["pre_close"] * (1 - df["limit_ratio"]).round(2)
        df.to_csv("today_base_info.csv", index=False)

    def get_his_data(self):
        # 获取更多天数的历史数据以便计算连板天数
        data = self.qmt_data.get_market_data_ex(
            stock_list=self.stock_list,
            period="1d",
            count=30  # 获取30天数据用于计算连板天数
        )

        # 存储各个指标的结果
        results = {
            "指标30": {"个股": [], "最大天数": 0},
            "指标30A": {"个股": [], "数量": 0},
            "指标30B": {"个股": [], "数量": 0},
            "指标30C": {"个股": [], "数量": 0},
            "指标30D": {"个股": [], "数量": 0}
        }

        # 新增涨停统计结果
        limit_up_stats = {
            "昨日涨停总数": 0,
            "昨日连板家数": 0,
            "昨日涨停股票详情": [],  # 包含股票代码和连板数
            "连板分布": {}  # 各连板数的股票数量
        }
    
        for stock_code, df in data.items():
            if df.empty or len(df) < 3:
                continue
            # 添加必要的计算字段
            limit_ratio = get_stock_ratio(stock_code)
            df["股票代码"] = stock_code
            df["最大涨幅"] = limit_ratio
            df["涨停价"] = (df["preClose"] * (1 + limit_ratio)).round(2)
            df["跌停价"] = (df["preClose"] * (1 - limit_ratio)).round(2)
            # 判断是否涨停
            df["是否涨停"] = df.apply(lambda x: round(x["close"], 2) == round(x["涨停价"], 2), axis=1)
            # 判断是否一字板（开盘价=最高价=最低价=收盘价，且涨停）
            df["是否一字板"] = df["是否涨停"] & (df["open"] == df["high"]) & (df["high"] == df["low"]) & (df["low"] == df["close"])

            # 按时间排序（最新的在最后）
            df = df.sort_index()

            # 计算各个指标
            self._calculate_indicators(stock_code, df, results, limit_up_stats)

        # 保存涨停统计结果
        self._save_limit_up_stats(limit_up_stats)

        # 保存指标30系列结果
        self._save_indicator_results(results)
        # 保存结果
        return results, data

    def _calculate_indicators(self, stock_code, df, results, limit_up_stats):
        """计算单个股票的各项指标和涨停统计"""
        if len(df) < 3:
            return

        # 获取最近几天的数据
        latest_day = df.iloc[-1]  # 昨日数据
        prev_day = df.iloc[-2] if len(df) >= 2 else None  # 前一日数据

        # 指标30A: 昨日一字板
        if latest_day["是否一字板"]:
            results["指标30A"]["个股"].append(stock_code)
            results["指标30A"]["数量"] += 1

        # 指标30B: 前一日一字板
        if prev_day is not None and prev_day["是否一字板"]:
            results["指标30B"]["个股"].append(stock_code)
            results["指标30B"]["数量"] += 1

        # 指标30C: 昨日一字板且前日最高价小于前日涨停价
        if (latest_day["是否一字板"] and prev_day is not None and
            round(prev_day["high"], 2) < round(prev_day["涨停价"], 2)):
            results["指标30C"]["个股"].append(stock_code)
            results["指标30C"]["数量"] += 1

        # 指标30D: 一字起板（从昨日开始连续涨停序列的第一个涨停为一字板）
        first_consecutive_limit_up_is_yizi = self._check_first_consecutive_limit_up_is_yizi(df)
        if first_consecutive_limit_up_is_yizi:
            results["指标30D"]["个股"].append(stock_code)
            results["指标30D"]["数量"] += 1

        # 指标30: 计算最大一字连板天数
        max_consecutive_days = self._calculate_max_consecutive_yizi_days(df)
        if max_consecutive_days > results["指标30"]["最大天数"]:
            results["指标30"]["最大天数"] = max_consecutive_days
            results["指标30"]["个股"] = [stock_code]
        elif max_consecutive_days == results["指标30"]["最大天数"] and max_consecutive_days > 0:
            results["指标30"]["个股"].append(stock_code)

        # 新增：涨停统计
        if latest_day["是否涨停"]:
            limit_up_stats["昨日涨停总数"] += 1

            # 计算连板数
            consecutive_days = self._calculate_consecutive_limit_up_days(df)

            # 记录涨停股票详情
            stock_info = {
                "股票代码": stock_code,
                "连板数": consecutive_days,
                "是否一字板": latest_day["是否一字板"],
                "收盘价": latest_day["close"],
                "涨停价": latest_day["涨停价"]
            }
            limit_up_stats["昨日涨停股票详情"].append(stock_info)

            # 统计连板家数
            if consecutive_days > 1:
                limit_up_stats["昨日连板家数"] += 1

            # 连板分布统计
            if consecutive_days not in limit_up_stats["连板分布"]:
                limit_up_stats["连板分布"][consecutive_days] = 0
            limit_up_stats["连板分布"][consecutive_days] += 1

    def _check_first_consecutive_limit_up_is_yizi(self, df):
        """检查从昨日开始连续涨停序列的第一个涨停是否为一字板"""
        if df.empty:
            return False
        # 从最新日期（昨日）开始往前找连续涨停序列
        limit_up_flags = df["是否涨停"].values
        yizi_flags = df["是否一字板"].values

        # 如果昨日不是涨停，直接返回False
        if not limit_up_flags[-1]:
            return False

        # 从最新日期往前找连续涨停的起始位置
        consecutive_start_idx = len(limit_up_flags) - 1
        for i in range(len(limit_up_flags) - 1, -1, -1):
            if limit_up_flags[i]:
                consecutive_start_idx = i
            else:
                break

        # 检查连续涨停序列的第一个涨停是否为一字板
        return yizi_flags[consecutive_start_idx]

    def _calculate_max_consecutive_yizi_days(self, df):
        """计算最大连续一字板天数"""
        if df.empty:
            return 0

        # 获取一字板标记
        yizi_flags = df["是否一字板"].values

        max_consecutive = 0
        current_consecutive = 0

        # 从最新日期往前计算连续天数
        for i in range(len(yizi_flags) - 1, -1, -1):
            if yizi_flags[i]:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_consecutive_limit_up_days(self, df):
        """计算从昨日开始的连续涨停天数"""
        if df.empty:
            return 0

        limit_up_flags = df["是否涨停"].values
        consecutive_days = 0

        # 从最新日期往前计算连续涨停天数
        for i in range(len(limit_up_flags) - 1, -1, -1):
            if limit_up_flags[i]:
                consecutive_days += 1
            else:
                break

        return consecutive_days

    def _save_indicator_results(self, results):
        """保存指标结果到文件"""
        import json
        from datetime import datetime

        # 添加时间戳
        results["统计时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存为JSON文件
        with open("indicator_30_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 打印结果摘要
        print("\n=== 指标30系列统计结果 ===")
        print(f"指标30 - 最大一字连板天数: {results['指标30']['最大天数']}天")
        print(f"         对应个股数量: {len(results['指标30']['个股'])}个")
        print(f"指标30A - 昨日一字板个股数量: {results['指标30A']['数量']}个")
        print(f"指标30B - 前一日一字板个股数量: {results['指标30B']['数量']}个")
        print(f"指标30C - 昨日一字板且前日最高价小于前日涨停价: {results['指标30C']['数量']}个")
        print(f"指标30D - 一字起板个股数量: {results['指标30D']['数量']}个")
        print(f"\n详细结果已保存到: indicator_30_results.json")

    def _save_limit_up_stats(self, stats):
        """保存涨停统计结果"""
        import json
        from datetime import datetime

        # 添加时间戳
        stats["统计时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存为JSON文件
        with open("limit_up_stats.json", "w", encoding="utf-8") as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        # 打印统计结果
        print("\n=== 昨日涨停统计结果 ===")
        print(f"昨日涨停总数: {stats['昨日涨停总数']}只")
        print(f"昨日连板家数: {stats['昨日连板家数']}只")

        if stats["连板分布"]:
            print(f"\n连板分布:")
            for days, count in sorted(stats["连板分布"].items()):
                if days == 1:
                    print(f"  首板: {count}只")
                else:
                    print(f"  {days}连板: {count}只")

        if stats["昨日涨停股票详情"]:
            print(f"\n前10只涨停股详情:")
            for i, stock in enumerate(stats["昨日涨停股票详情"][:10]):
                board_type = "一字板" if stock["是否一字板"] else "普通板"
                print(f"  {i+1}. {stock['股票代码']} - {stock['连板数']}连板 ({board_type})")

            if len(stats["昨日涨停股票详情"]) > 10:
                print(f"  ... 还有{len(stats['昨日涨停股票详情']) - 10}只股票")

        print(f"\n详细结果已保存到: limit_up_stats.json")


if __name__ == "__main__":
    save_info = SaveInfo()
    # 运行历史数据统计（包含原有功能和新增涨停统计）
    infos = save_info.get_his_data()
    print("统计完成！")