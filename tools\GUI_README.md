# 量化交易系统GUI管理工具

基于PyQt5开发的专业级量化交易系统管理界面，提供QMT连接管理、策略调度、指标测试等完整功能。

## 🚀 主要功能

### 1. QMT连接管理
- **连接配置**: QMT路径、会话ID、资金账号配置
- **连接测试**: 实时测试QMT数据和交易接口连接状态
- **状态监控**: 实时显示连接状态和最后更新时间
- **自动重连**: 支持连接断开后自动重连

### 2. 策略管理
- **策略添加**: 支持添加自定义策略文件
- **策略编辑**: 修改策略配置和参数
- **策略删除**: 安全删除策略（运行中策略会先停止）
- **运行控制**: 启动、停止策略
- **定时运行**: 支持定时启动和指定时间段运行
- **状态监控**: 实时显示策略运行状态

### 3. 指标测试
- **指标选择**: 内置常用技术指标（MA、RSI、MACD、布林带等）
- **参数配置**: 智能参数输入界面，显示参数类型和说明
- **参数验证**: 验证参数有效性
- **快速运行**: 一键运行指标并查看结果
- **结果显示**: 详细的运行结果和执行时间

### 4. 缓存管理
- **缓存配置**: 设置最大缓存数量和超时时间
- **自动清理**: 支持自动清理过期缓存
- **手动清理**: 一键清理所有缓存
- **状态监控**: 实时显示缓存数量和大小

### 5. 运行日志
- **实时日志**: 实时显示系统运行日志
- **日志级别**: 支持不同级别的日志过滤
- **自动滚动**: 自动滚动到最新日志
- **日志保存**: 支持将日志保存到文件

## 📦 安装和启动

### 方法1: 自动安装启动
```bash
# 运行启动脚本，会自动检查和安装依赖
python tools/start_gui.py
```

### 方法2: 手动安装
```bash
# 安装依赖
pip install -r tools/gui_requirements.txt

# 启动GUI
python tools/gui_main.py
```

### 依赖要求
- Python 3.7+
- PyQt5 5.15+
- 其他依赖见 `gui_requirements.txt`

## 🎯 界面布局

### 主窗口结构
```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 | 工具 | 帮助                                │
├─────────────┬───────────────────────────────────────────┤
│             │ 策略管理 | 指标测试 | 运行日志              │
│ QMT连接配置  │                                          │
│             │                                          │
│ 缓存管理     │          主要功能区域                      │
│             │                                          │
│             │                                          │
├─────────────┴───────────────────────────────────────────┤
│ 状态栏: QMT状态 | 运行策略数 | 缓存状态 | 当前时间        │
└─────────────────────────────────────────────────────────┘
```

### 左侧面板
- **QMT连接配置**: 配置和管理QMT连接
- **缓存管理**: 配置和监控系统缓存

### 右侧标签页
- **策略管理**: 策略的增删改查和运行控制
- **指标测试**: 技术指标的测试和验证
- **运行日志**: 系统运行日志的实时显示

## 📋 使用指南

### 1. 首次使用

#### 配置QMT连接
1. 在左侧"QMT连接配置"中填入：
   - QMT路径: 如 `D:\国金证券QMT交易端\userdata_mini`
   - 会话ID: 如 `123456`
   - 资金账号: 您的资金账号
   - 访问令牌: (可选)

2. 点击"测试连接"验证配置
3. 点击"连接QMT"建立连接

#### 配置缓存
1. 设置最大缓存数量（建议10000-50000）
2. 设置缓存超时时间（建议300秒）
3. 启用"自动清理过期缓存"

### 2. 策略管理

#### 添加策略
1. 切换到"策略管理"标签页
2. 点击"添加策略"按钮
3. 填写策略信息：
   - 策略名称: 自定义名称
   - 文件路径: 策略文件的完整路径
   - 策略类名: 策略类的名称
   - 策略描述: 策略的简要说明

#### 运行策略
1. 在策略列表中选择要运行的策略
2. 点击"启动策略"开始运行
3. 或点击"定时运行"设置定时任务

#### 定时运行
1. 选择策略后点击"定时运行"
2. 选择运行模式：
   - 手动运行: 需要手动启动
   - 定时运行: 按设定时间自动运行
   - 时间段运行: 在指定时间段内运行
3. 设置开始和结束时间

### 3. 指标测试

#### 测试指标
1. 切换到"指标测试"标签页
2. 从下拉菜单选择要测试的指标
3. 根据提示填入参数：
   - 必需参数标有 `*` 号
   - 鼠标悬停可查看参数说明
   - 列表参数可输入如 `[1,2,3,4,5]` 或使用测试数据
4. 点击"验证参数"检查参数有效性
5. 点击"运行指标"执行测试

#### 内置指标
- **移动平均线(MA)**: 计算简单/指数移动平均
- **相对强弱指标(RSI)**: 计算RSI值
- **MACD指标**: 计算MACD、信号线和柱状图
- **布林带**: 计算上轨、中轨、下轨

### 4. 日志监控

#### 查看日志
1. 切换到"运行日志"标签页
2. 选择日志级别进行过滤
3. 启用"自动滚动"查看最新日志

#### 日志管理
- **清空日志**: 清除当前显示的所有日志
- **保存日志**: 将日志保存到文本文件
- **日志级别**: DEBUG、INFO、WARNING、ERROR、CRITICAL

## ⚙️ 高级功能

### 菜单功能

#### 文件菜单
- **导入策略**: 从Python文件导入策略
- **导出策略**: 将策略配置导出为JSON文件
- **退出**: 安全退出程序

#### 工具菜单
- **清理缓存**: 清理所有系统缓存
- **重新连接QMT**: 重新建立QMT连接
- **系统设置**: 系统配置选项

#### 帮助菜单
- **关于**: 显示软件版本和功能信息

### 状态栏信息
- **QMT状态**: 显示QMT连接状态（绿色=已连接，红色=未连接）
- **运行策略**: 显示当前运行中的策略数量
- **缓存状态**: 显示当前缓存条数
- **当前时间**: 实时显示系统时间

### 快捷键
- `Ctrl+I`: 导入策略
- `Ctrl+E`: 导出策略
- `Ctrl+Q`: 退出程序

## 🔧 配置文件

程序会自动保存以下配置：
- QMT连接配置
- 缓存设置
- 窗口位置和大小

配置文件位置：
- Windows: `%APPDATA%/QuantTool/`
- Linux/Mac: `~/.config/QuantTool/`

## 🐛 故障排除

### 常见问题

1. **PyQt5导入失败**
   ```bash
   pip install PyQt5 PyQt5-tools
   ```

2. **QMT连接失败**
   - 检查QMT路径是否正确
   - 确认QMT程序正在运行
   - 验证会话ID和账号信息

3. **策略运行失败**
   - 检查策略文件路径
   - 确认策略类名正确
   - 查看运行日志中的错误信息

4. **界面显示异常**
   - 尝试重启程序
   - 检查屏幕分辨率和缩放设置

### 日志文件
程序运行日志会显示在"运行日志"标签页中，可以保存到文件进行详细分析。

## 📞 技术支持

如遇到问题，请：
1. 查看运行日志中的错误信息
2. 检查配置是否正确
3. 确认依赖包版本兼容性

## 🔄 版本更新

当前版本: v1.0.0

主要特性：
- ✅ QMT连接管理
- ✅ 策略管理和调度
- ✅ 指标测试框架
- ✅ 缓存管理
- ✅ 实时日志监控
- ✅ 专业界面设计

## 📄 许可证

本软件仅供学习和研究使用。
